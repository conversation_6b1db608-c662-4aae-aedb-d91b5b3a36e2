const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const { Worker } = require('worker_threads');
const path = require('path');

const app = express();
const server = http.createServer(app);
const io = socketIo(server);

const PORT = process.env.PORT || 3001;

// Serve static files
app.use(express.static('public'));
app.use(express.json());

// Store active workers
const activeWorkers = new Map();

// Socket.io connection handling
io.on('connection', (socket) => {
    console.log('Client connected:', socket.id);

    socket.on('start-generation', (data) => {
        const { walletCount, cryptoType, isInfinite, intensity, luckyMode } = data;

        // Create worker thread
        const worker = new Worker('./wallet-worker.js', {
            workerData: {
                walletCount: isInfinite ? -1 : walletCount,
                cryptoType,
                intensity: intensity || 'aggressive',
                socketId: socket.id,
                luckyMode: luckyMode || false
            }
        });

        // Store worker reference
        activeWorkers.set(socket.id, worker);

        // Handle messages from worker
        worker.on('message', (message) => {
            socket.emit('wallet-result', message);
        });

        // Handle worker errors
        worker.on('error', (error) => {
            console.error('Worker error:', error);
            socket.emit('error', { message: error.message });
        });

        // Handle worker exit
        worker.on('exit', (code) => {
            console.log(`Worker exited with code ${code}`);
            activeWorkers.delete(socket.id);
            socket.emit('generation-complete');
        });

        socket.emit('generation-started');
    });

    socket.on('stop-generation', () => {
        const worker = activeWorkers.get(socket.id);
        if (worker) {
            worker.terminate();
            activeWorkers.delete(socket.id);
            socket.emit('generation-stopped');
        }
    });

    socket.on('disconnect', () => {
        console.log('Client disconnected:', socket.id);
        const worker = activeWorkers.get(socket.id);
        if (worker) {
            worker.terminate();
            activeWorkers.delete(socket.id);
        }
    });
});

// API Routes
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

app.get('/api/status', (req, res) => {
    res.json({
        activeWorkers: activeWorkers.size,
        uptime: process.uptime()
    });
});

// Manual balance checking endpoint
app.post('/api/check-balance', async (req, res) => {
    try {
        const { address, cryptoType } = req.body;

        if (!address || !cryptoType) {
            return res.status(400).json({ error: 'Address and cryptoType are required' });
        }

        // Create a temporary worker for balance checking
        const { Worker } = require('worker_threads');
        const balanceWorker = new Worker('./balance-checker.js', {
            workerData: { address, cryptoType }
        });

        balanceWorker.on('message', (result) => {
            res.json(result);
            balanceWorker.terminate();
        });

        balanceWorker.on('error', (error) => {
            res.status(500).json({ error: error.message });
            balanceWorker.terminate();
        });

        // Timeout after 30 seconds
        setTimeout(() => {
            balanceWorker.terminate();
            res.status(408).json({ error: 'Request timeout' });
        }, 30000);

    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

server.listen(PORT, () => {
    console.log(`Server running on http://localhost:${PORT}`);
});
