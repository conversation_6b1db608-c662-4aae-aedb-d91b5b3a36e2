// Initialize Socket.IO connection
const socket = io();

// DOM elements
const startBtn = document.getElementById('startBtn');
const luckyBtn = document.getElementById('luckyBtn');
const stopBtn = document.getElementById('stopBtn');
const clearBtn = document.getElementById('clearBtn');
const cryptoTypeSelect = document.getElementById('cryptoType');
const walletCountInput = document.getElementById('walletCount');
const infiniteModeCheckbox = document.getElementById('infiniteMode');
const intensitySelect = document.getElementById('intensity');
const luckyModeCheckbox = document.getElementById('luckyMode');
const statusSpan = document.getElementById('status');
const processedSpan = document.getElementById('processed');
const foundWithBalanceSpan = document.getElementById('foundWithBalance');
const successRateSpan = document.getElementById('successRate');
const progressFill = document.getElementById('progressFill');
const progressText = document.getElementById('progressText');
const resultsList = document.getElementById('resultsList');

// Console elements
const consoleContent = document.getElementById('consoleContent');
const clearConsoleBtn = document.getElementById('clearConsole');
const toggleConsoleBtn = document.getElementById('toggleConsole');
const autoScrollCheckbox = document.getElementById('autoScroll');

// Statistics elements
const totalGeneratedSpan = document.getElementById('totalGenerated');
const totalWithBalanceSpan = document.getElementById('totalWithBalance');
const totalBalanceSpan = document.getElementById('totalBalance');
const avgTimeSpan = document.getElementById('avgTime');

// Probability statistics elements
const veryHighProbSpan = document.getElementById('veryHighProb');
const highProbSpan = document.getElementById('highProb');
const mediumProbSpan = document.getElementById('mediumProb');
const lowProbSpan = document.getElementById('lowProb');
const veryLowProbSpan = document.getElementById('veryLowProb');

// High probability summary elements
const highProbabilitySummary = document.getElementById('highProbabilitySummary');
const highProbabilityList = document.getElementById('highProbabilityList');

// State variables
let isRunning = false;
let startTime = null;
let processedCount = 0;
let foundWithBalanceCount = 0;
let totalBalance = 0;
let results = [];

// Event listeners
startBtn.addEventListener('click', startGeneration);
luckyBtn.addEventListener('click', startLuckyGeneration);
stopBtn.addEventListener('click', stopGeneration);
clearBtn.addEventListener('click', clearResults);
infiniteModeCheckbox.addEventListener('change', toggleInfiniteMode);

// Console event listeners
clearConsoleBtn.addEventListener('click', clearConsole);
toggleConsoleBtn.addEventListener('click', toggleConsole);

// Filter and sort event listeners
const probabilityFilter = document.getElementById('probabilityFilter');
const sortByProbabilityBtn = document.getElementById('sortByProbability');
probabilityFilter.addEventListener('change', filterByProbability);
sortByProbabilityBtn.addEventListener('click', sortByProbability);

// Socket event listeners
socket.on('generation-started', () => {
    isRunning = true;
    startTime = Date.now();
    updateUI();
    statusSpan.textContent = 'Generating wallets...';
});

socket.on('generation-stopped', () => {
    isRunning = false;
    updateUI();
    statusSpan.textContent = 'Stopped';
});

socket.on('generation-complete', () => {
    isRunning = false;
    updateUI();
    statusSpan.textContent = 'Complete';
});

socket.on('wallet-result', (message) => {
    handleWalletResult(message);
});

socket.on('wallet-with-balance', (message) => {
    // Special handling for wallets with balance
    console.log('🎉 Found wallet with balance!', message.data);
    showNotification(`Found wallet with ${message.data.balance} balance!`, 'success');
    addConsoleMessage(`🎉 Found wallet with balance: ${message.data.balance} ${message.data.type}`, 'success');
});

socket.on('console', (message) => {
    addConsoleMessage(message.message, message.level);
});

socket.on('error', (error) => {
    console.error('Error:', error);
    statusSpan.textContent = `Error: ${error.message}`;
    isRunning = false;
    updateUI();
});

function startGeneration() {
    const cryptoType = cryptoTypeSelect.value;
    const walletCount = parseInt(walletCountInput.value);
    const isInfinite = infiniteModeCheckbox.checked;
    const intensity = intensitySelect.value;

    if (!isInfinite && (!walletCount || walletCount < 1)) {
        alert('Please enter a valid number of wallets');
        return;
    }

    // Reset counters
    processedCount = 0;
    foundWithBalanceCount = 0;
    totalBalance = 0;
    results = [];

    // Add console message
    addConsoleMessage(`🚀 Starting ${cryptoType} wallet generation (${isInfinite ? 'infinite' : walletCount} wallets, ${intensity} intensity)`, 'info');

    socket.emit('start-generation', {
        walletCount,
        cryptoType,
        isInfinite,
        intensity,
        luckyMode: false
    });
}

function startLuckyGeneration() {
    const cryptoType = cryptoTypeSelect.value;
    const walletCount = parseInt(walletCountInput.value);
    const isInfinite = infiniteModeCheckbox.checked;
    const intensity = intensitySelect.value;

    if (!isInfinite && (!walletCount || walletCount < 1)) {
        alert('Please enter a valid number of wallets');
        return;
    }

    // Reset counters
    processedCount = 0;
    foundWithBalanceCount = 0;
    totalBalance = 0;
    results = [];

    // Add console message
    addConsoleMessage(`🍀 Starting LUCKY ${cryptoType} wallet generation with advanced prediction algorithms!`, 'success');
    addConsoleMessage(`⚡ Using invisible prediction power - no balance checking needed!`, 'info');

    socket.emit('start-generation', {
        walletCount,
        cryptoType,
        isInfinite,
        intensity,
        luckyMode: true
    });
}

function stopGeneration() {
    socket.emit('stop-generation');
}

function clearResults() {
    resultsList.innerHTML = '';
    results = [];
    processedCount = 0;
    foundWithBalanceCount = 0;
    totalBalance = 0;

    // Clear high probability summary
    highProbabilityList.innerHTML = '';
    highProbabilitySummary.style.display = 'none';

    updateStatistics();
    updateProgress(0, infiniteModeCheckbox.checked ? 'infinite' : parseInt(walletCountInput.value));
    statusSpan.textContent = 'Ready';
}

function toggleInfiniteMode() {
    walletCountInput.disabled = infiniteModeCheckbox.checked;
    if (infiniteModeCheckbox.checked) {
        walletCountInput.placeholder = 'Infinite mode enabled';
    } else {
        walletCountInput.placeholder = 'Enter number of wallets';
    }
}

function handleWalletResult(message) {
    if (message.type === 'wallet') {
        const walletData = message.data;
        processedCount = message.processed;

        // Add to results
        results.push(walletData);

        // Update counters
        if (walletData.balance && walletData.balance > 0) {
            foundWithBalanceCount++;
            totalBalance += walletData.balance;
        }

        // Update UI with enhanced probability handling
        addResultToListEnhanced(walletData);
        updateProgress(message.processed, message.total);
        updateStatistics();

        processedSpan.textContent = message.processed;
        foundWithBalanceSpan.textContent = foundWithBalanceCount;
        
        // Update success rate
        if (processedCount > 0) {
            const successRate = (foundWithBalanceCount / processedCount) * 100;
            successRateSpan.textContent = `${successRate.toFixed(4)}%`;
        }
    } else if (message.type === 'status') {
        statusSpan.textContent = message.message;
    } else if (message.type === 'error') {
        console.error('Worker error:', message.message);
    }
}

function addResultToList(walletData) {
    const resultItem = document.createElement('div');
    resultItem.className = 'result-item';
    
    if (walletData.balance && walletData.balance > 0) {
        resultItem.classList.add('has-balance');
    }

    // Add lucky styling if it's a lucky wallet
    if (walletData.luckyScore) {
        resultItem.classList.add('lucky-wallet');
        resultItem.style.borderLeft = '4px solid #ffd700';
        resultItem.style.background = 'linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%)';
    }

    const hasBalance = walletData.balance && walletData.balance > 0;
    const balanceClass = hasBalance ? 'balance' : 'balance zero';
    // Get crypto symbol based on wallet type
    let cryptoSymbol = 'BTC';
    if (walletData.type === 'Ethereum') cryptoSymbol = 'ETH';
    else if (walletData.type === 'Litecoin') cryptoSymbol = 'LTC';
    else if (walletData.type === 'Dogecoin') cryptoSymbol = 'DOGE';
    
    const balanceText = hasBalance ? 
        `${walletData.balance.toFixed(8)} ${cryptoSymbol}` : 
        '0';

    // Add lucky score display
    const luckyInfo = walletData.luckyScore ? 
        `<div class="lucky-score">🍀 ${walletData.algorithm} (${walletData.luckyScore.toFixed(2)})</div>` : 
        '';

    // Calculate probability display with enhanced styling
    let probabilityText = 'N/A';
    let probabilityClass = 'probability-very-low';
    let resultItemProbabilityClass = '';

    if (walletData.transactionProbability !== undefined) {
        const prob = walletData.transactionProbability;
        probabilityText = `${prob.toFixed(3)}%`;

        // Enhanced probability classification
        if (prob >= 5.0) {
            probabilityClass = 'probability-very-high';
            resultItemProbabilityClass = 'probability-very-high';
        } else if (prob >= 1.0) {
            probabilityClass = 'probability-high';
            resultItemProbabilityClass = 'probability-high';
        } else if (prob >= 0.5) {
            probabilityClass = 'probability-medium';
            resultItemProbabilityClass = 'probability-medium';
        } else if (prob >= 0.1) {
            probabilityClass = 'probability-low';
            resultItemProbabilityClass = 'probability-low';
        } else {
            probabilityClass = 'probability-very-low';
            resultItemProbabilityClass = 'probability-very-low';
        }

        // Add special emoji indicators for high probability
        if (prob >= 5.0) {
            probabilityText = `🔥 ${probabilityText}`;
        } else if (prob >= 1.0) {
            probabilityText = `⭐ ${probabilityText}`;
        } else if (prob >= 0.5) {
            probabilityText = `🎯 ${probabilityText}`;
        } else if (prob >= 0.1) {
            probabilityText = `📊 ${probabilityText}`;
        }
    }

    // Apply probability-based styling to the entire result item
    if (resultItemProbabilityClass) {
        resultItem.classList.add(resultItemProbabilityClass);
    }

    // Create unique ID for this wallet
    const walletId = `wallet_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    resultItem.innerHTML = `
        <div class="address" title="${walletData.address || 'N/A'}">${walletData.address || 'N/A'}</div>
        <div class="private-key" title="${walletData.privateKey || 'N/A'}">${walletData.privateKey || 'N/A'}</div>
        <div class="${balanceClass}" id="balance_${walletId}">${balanceText}</div>
        <div>${walletData.type || 'Unknown'}</div>
        <div>${new Date(walletData.timestamp).toLocaleTimeString()}</div>
        <div class="${probabilityClass}">${probabilityText}</div>
        <div>
            <button class="check-balance-btn" onclick="checkWalletBalance('${walletData.address}', '${walletData.type}', '${walletId}')">
                Check Balance
            </button>
        </div>
        ${luckyInfo}
    `;

    // Insert at the top for latest results
    resultsList.insertBefore(resultItem, resultsList.firstChild);

    // Limit displayed results to prevent performance issues
    const maxDisplayedResults = 100;
    while (resultsList.children.length > maxDisplayedResults) {
        resultsList.removeChild(resultsList.lastChild);
    }
}

function updateProgress(processed, total) {
    if (total === 'infinite') {
        progressFill.style.width = '100%';
        progressText.textContent = `${processed} wallets processed`;
    } else {
        const percentage = (processed / total) * 100;
        progressFill.style.width = `${percentage}%`;
        progressText.textContent = `${processed}/${total} (${percentage.toFixed(1)}%)`;
    }
}

function updateStatistics() {
    totalGeneratedSpan.textContent = processedCount;
    totalWithBalanceSpan.textContent = foundWithBalanceCount;

    // Get crypto symbol based on selected type
    const cryptoType = cryptoTypeSelect.value;
    let cryptoSymbol = 'BTC';
    if (cryptoType === 'ethereum') cryptoSymbol = 'ETH';
    else if (cryptoType === 'litecoin') cryptoSymbol = 'LTC';
    else if (cryptoType === 'dogecoin') cryptoSymbol = 'DOGE';

    totalBalanceSpan.textContent = `${totalBalance.toFixed(8)} ${cryptoSymbol}`;

    if (startTime && processedCount > 0) {
        const elapsed = Date.now() - startTime;
        const avgTime = elapsed / processedCount;
        avgTimeSpan.textContent = `${avgTime.toFixed(0)}ms`;
    }

    // Update probability statistics
    updateProbabilityStatistics();
}

function updateProbabilityStatistics() {
    let veryHighCount = 0;
    let highCount = 0;
    let mediumCount = 0;
    let lowCount = 0;
    let veryLowCount = 0;

    // Count wallets by probability ranges
    results.forEach(wallet => {
        if (wallet.transactionProbability !== undefined) {
            const prob = wallet.transactionProbability;
            if (prob >= 5.0) {
                veryHighCount++;
            } else if (prob >= 1.0) {
                highCount++;
            } else if (prob >= 0.5) {
                mediumCount++;
            } else if (prob >= 0.1) {
                lowCount++;
            } else {
                veryLowCount++;
            }
        }
    });

    // Update the display
    veryHighProbSpan.textContent = veryHighCount;
    highProbSpan.textContent = highCount;
    mediumProbSpan.textContent = mediumCount;
    lowProbSpan.textContent = lowCount;
    veryLowProbSpan.textContent = veryLowCount;
}

function updateUI() {
    startBtn.disabled = isRunning;
    stopBtn.disabled = !isRunning;
    cryptoTypeSelect.disabled = isRunning;
    walletCountInput.disabled = isRunning || infiniteModeCheckbox.checked;
    infiniteModeCheckbox.disabled = isRunning;
    intensitySelect.disabled = isRunning;
}

// Console functions
function addConsoleMessage(message, level = 'info') {
    const timestamp = new Date().toLocaleTimeString();
    const consoleLine = document.createElement('div');
    consoleLine.className = `console-line ${level}`;
    consoleLine.innerHTML = `<span style="color: #888;">[${timestamp}]</span> ${message}`;
    
    consoleContent.appendChild(consoleLine);
    
    // Auto-scroll if enabled
    if (autoScrollCheckbox.checked) {
        consoleContent.scrollTop = consoleContent.scrollHeight;
    }
    
    // Limit console lines to prevent memory issues
    const maxLines = 1000;
    while (consoleContent.children.length > maxLines) {
        consoleContent.removeChild(consoleContent.firstChild);
    }
}

function clearConsole() {
    consoleContent.innerHTML = '<div class="console-line">Console cleared...</div>';
}

function toggleConsole() {
    const isVisible = consoleContent.style.display !== 'none';
    consoleContent.style.display = isVisible ? 'none' : 'block';
    toggleConsoleBtn.textContent = isVisible ? 'Show Console' : 'Hide Console';
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    
    // Style the notification
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#28a745' : '#007bff'};
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 1000;
        animation: slideIn 0.3s ease-out;
        max-width: 300px;
        word-wrap: break-word;
    `;
    
    document.body.appendChild(notification);
    
    // Remove after 5 seconds
    setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease-in';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 5000);
}

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    @keyframes slideOut {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
`;
document.head.appendChild(style);

// Filter and sort functions
function filterByProbability() {
    const filterValue = probabilityFilter.value;
    const resultItems = resultsList.querySelectorAll('.result-item');

    resultItems.forEach(item => {
        const probabilityElement = item.querySelector('.probability-very-high, .probability-high, .probability-medium, .probability-low, .probability-very-low');

        if (!probabilityElement) {
            item.style.display = 'grid';
            return;
        }

        const probabilityText = probabilityElement.textContent.replace(/[^\d.]/g, '');
        const probability = parseFloat(probabilityText) || 0;

        let shouldShow = true;

        switch (filterValue) {
            case 'high':
                shouldShow = probability >= 1.0;
                break;
            case 'medium':
                shouldShow = probability >= 0.5;
                break;
            case 'low':
                shouldShow = probability >= 0.1;
                break;
            case 'all':
            default:
                shouldShow = true;
                break;
        }

        item.style.display = shouldShow ? 'grid' : 'none';
    });

    // Update filter status
    const visibleCount = Array.from(resultItems).filter(item => item.style.display !== 'none').length;
    addConsoleMessage(`🔍 Filter applied: Showing ${visibleCount} of ${resultItems.length} wallets`, 'info');
}

function sortByProbability() {
    const resultItems = Array.from(resultsList.querySelectorAll('.result-item'));

    // Sort by probability (highest first)
    resultItems.sort((a, b) => {
        const getProbability = (item) => {
            const probabilityElement = item.querySelector('.probability-very-high, .probability-high, .probability-medium, .probability-low, .probability-very-low');
            if (!probabilityElement) return 0;
            const probabilityText = probabilityElement.textContent.replace(/[^\d.]/g, '');
            return parseFloat(probabilityText) || 0;
        };

        return getProbability(b) - getProbability(a);
    });

    // Clear and re-append sorted items
    resultsList.innerHTML = '';
    resultItems.forEach(item => {
        resultsList.appendChild(item);
    });

    addConsoleMessage(`📊 Results sorted by transaction probability (highest first)`, 'info');
}

// Enhanced wallet result handling with probability focus
function addResultToListEnhanced(walletData) {
    // If this is a high-probability wallet, prioritize it
    if (walletData.transactionProbability >= 1.0) {
        addConsoleMessage(`🎯 HIGH PROBABILITY WALLET FOUND: ${walletData.address.substring(0, 12)}... (${walletData.transactionProbability.toFixed(3)}%)`, 'success');

        // Add to high probability summary
        addToHighProbabilitySummary(walletData);

        // Show notification for very high probability wallets
        if (walletData.transactionProbability >= 5.0) {
            showNotification(`🔥 VERY HIGH probability wallet found: ${walletData.transactionProbability.toFixed(3)}%`, 'success');
        }
    }

    // Call the original function
    addResultToList(walletData);
}

// Add wallet to high probability summary
function addToHighProbabilitySummary(walletData) {
    // Show the summary section if it's hidden
    if (highProbabilitySummary.style.display === 'none') {
        highProbabilitySummary.style.display = 'block';
    }

    // Create high probability item
    const highProbItem = document.createElement('div');
    highProbItem.className = 'high-probability-item';

    const prob = walletData.transactionProbability;
    let emoji = '⭐';
    if (prob >= 5.0) emoji = '🔥';
    else if (prob >= 2.0) emoji = '💎';

    highProbItem.innerHTML = `
        <div class="address" title="${walletData.address}">${walletData.address}</div>
        <div class="private-key" title="${walletData.privateKey}">${walletData.privateKey}</div>
        <div class="probability">${emoji} ${prob.toFixed(3)}%</div>
        <div class="crypto-type">${walletData.type}</div>
    `;

    // Insert at the top (most recent first)
    highProbabilityList.insertBefore(highProbItem, highProbabilityList.firstChild);

    // Limit to top 10 high probability wallets
    while (highProbabilityList.children.length > 10) {
        highProbabilityList.removeChild(highProbabilityList.lastChild);
    }
}

// Manual balance checking function
async function checkWalletBalance(address, cryptoType, walletId) {
    const button = event.target;
    const balanceElement = document.getElementById(`balance_${walletId}`);

    // Update button state
    button.disabled = true;
    button.textContent = 'Checking...';
    button.classList.add('checking');

    // Update balance display
    balanceElement.textContent = 'Checking...';
    balanceElement.className = 'balance checking';

    try {
        addConsoleMessage(`🔍 Starting balance check for ${address.substring(0, 12)}... (${cryptoType})`, 'info');

        const response = await fetch('/api/check-balance', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                address: address,
                cryptoType: cryptoType.toLowerCase()
            })
        });

        const result = await response.json();

        if (result.success) {
            // Update balance display
            const balance = result.balance;
            let cryptoSymbol = 'BTC';
            if (cryptoType === 'Ethereum') cryptoSymbol = 'ETH';
            else if (cryptoType === 'Litecoin') cryptoSymbol = 'LTC';
            else if (cryptoType === 'Dogecoin') cryptoSymbol = 'DOGE';

            const balanceText = balance > 0 ?
                `${balance.toFixed(8)} ${cryptoSymbol}` :
                '0';

            balanceElement.textContent = balanceText;
            balanceElement.className = balance > 0 ? 'balance' : 'balance zero';

            // Update button state
            button.textContent = balance > 0 ? '✅ Found!' : '✅ Checked';
            button.classList.remove('checking');
            button.classList.add(balance > 0 ? 'success' : 'success');

            // Log detailed results to console
            addConsoleMessage(`✅ Balance Check Complete for ${address.substring(0, 12)}...`, 'success');
            addConsoleMessage(`💰 Balance: ${balanceText}`, balance > 0 ? 'success' : 'info');
            addConsoleMessage(`📊 Transactions: ${result.transactions || 0}`, 'info');
            addConsoleMessage(`🌐 API Used: ${result.api || 'Unknown'}`, 'info');

            // Log full API response
            if (result.fullResponse) {
                addConsoleMessage(`📋 Full API Response:`, 'info');
                addConsoleMessage(`${JSON.stringify(result.fullResponse, null, 2)}`, 'info');
            }

            // Show notification for wallets with balance
            if (balance > 0) {
                showNotification(`🎉 Found balance: ${balanceText} in ${address.substring(0, 12)}...`, 'success');

                // Update global statistics
                foundWithBalanceCount++;
                totalBalance += balance;
                updateStatistics();
            }

        } else {
            // Handle error
            balanceElement.textContent = 'Error';
            balanceElement.className = 'balance error';

            button.textContent = '❌ Error';
            button.classList.remove('checking');
            button.classList.add('error');

            addConsoleMessage(`❌ Balance Check Failed for ${address.substring(0, 12)}...`, 'error');
            addConsoleMessage(`Error: ${result.error}`, 'error');

            if (result.fullResponse) {
                addConsoleMessage(`📋 Error Response:`, 'error');
                addConsoleMessage(`${JSON.stringify(result.fullResponse, null, 2)}`, 'error');
            }
        }

    } catch (error) {
        // Handle network/fetch errors
        balanceElement.textContent = 'Network Error';
        balanceElement.className = 'balance error';

        button.textContent = '❌ Failed';
        button.classList.remove('checking');
        button.classList.add('error');

        addConsoleMessage(`❌ Network Error for ${address.substring(0, 12)}...`, 'error');
        addConsoleMessage(`Error: ${error.message}`, 'error');
    }

    // Re-enable button after 3 seconds
    setTimeout(() => {
        button.disabled = false;
        if (button.classList.contains('error')) {
            button.textContent = 'Retry';
            button.classList.remove('error');
        }
    }, 3000);
}

// Make function globally available
window.checkWalletBalance = checkWalletBalance;

// Initialize UI
updateUI();
