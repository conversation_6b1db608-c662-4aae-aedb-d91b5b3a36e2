// Initialize Socket.IO connection
const socket = io();

// DOM elements
const startBtn = document.getElementById('startBtn');
const luckyBtn = document.getElementById('luckyBtn');
const stopBtn = document.getElementById('stopBtn');
const clearBtn = document.getElementById('clearBtn');
const cryptoTypeSelect = document.getElementById('cryptoType');
const walletCountInput = document.getElementById('walletCount');
const infiniteModeCheckbox = document.getElementById('infiniteMode');
const intensitySelect = document.getElementById('intensity');
const luckyModeCheckbox = document.getElementById('luckyMode');
const statusSpan = document.getElementById('status');
const processedSpan = document.getElementById('processed');
const foundWithBalanceSpan = document.getElementById('foundWithBalance');
const successRateSpan = document.getElementById('successRate');
const progressFill = document.getElementById('progressFill');
const progressText = document.getElementById('progressText');
const resultsList = document.getElementById('resultsList');

// Console elements
const consoleContent = document.getElementById('consoleContent');
const clearConsoleBtn = document.getElementById('clearConsole');
const toggleConsoleBtn = document.getElementById('toggleConsole');
const autoScrollCheckbox = document.getElementById('autoScroll');

// Statistics elements
const totalGeneratedSpan = document.getElementById('totalGenerated');
const totalWithBalanceSpan = document.getElementById('totalWithBalance');
const totalBalanceSpan = document.getElementById('totalBalance');
const avgTimeSpan = document.getElementById('avgTime');

// State variables
let isRunning = false;
let startTime = null;
let processedCount = 0;
let foundWithBalanceCount = 0;
let totalBalance = 0;
let results = [];

// Event listeners
startBtn.addEventListener('click', startGeneration);
luckyBtn.addEventListener('click', startLuckyGeneration);
stopBtn.addEventListener('click', stopGeneration);
clearBtn.addEventListener('click', clearResults);
infiniteModeCheckbox.addEventListener('change', toggleInfiniteMode);

// Console event listeners
clearConsoleBtn.addEventListener('click', clearConsole);
toggleConsoleBtn.addEventListener('click', toggleConsole);

// Socket event listeners
socket.on('generation-started', () => {
    isRunning = true;
    startTime = Date.now();
    updateUI();
    statusSpan.textContent = 'Generating wallets...';
});

socket.on('generation-stopped', () => {
    isRunning = false;
    updateUI();
    statusSpan.textContent = 'Stopped';
});

socket.on('generation-complete', () => {
    isRunning = false;
    updateUI();
    statusSpan.textContent = 'Complete';
});

socket.on('wallet-result', (message) => {
    handleWalletResult(message);
});

socket.on('wallet-with-balance', (message) => {
    // Special handling for wallets with balance
    console.log('🎉 Found wallet with balance!', message.data);
    showNotification(`Found wallet with ${message.data.balance} balance!`, 'success');
    addConsoleMessage(`🎉 Found wallet with balance: ${message.data.balance} ${message.data.type}`, 'success');
});

socket.on('console', (message) => {
    addConsoleMessage(message.message, message.level);
});

socket.on('error', (error) => {
    console.error('Error:', error);
    statusSpan.textContent = `Error: ${error.message}`;
    isRunning = false;
    updateUI();
});

function startGeneration() {
    const cryptoType = cryptoTypeSelect.value;
    const walletCount = parseInt(walletCountInput.value);
    const isInfinite = infiniteModeCheckbox.checked;
    const intensity = intensitySelect.value;

    if (!isInfinite && (!walletCount || walletCount < 1)) {
        alert('Please enter a valid number of wallets');
        return;
    }

    // Reset counters
    processedCount = 0;
    foundWithBalanceCount = 0;
    totalBalance = 0;
    results = [];

    // Add console message
    addConsoleMessage(`🚀 Starting ${cryptoType} wallet generation (${isInfinite ? 'infinite' : walletCount} wallets, ${intensity} intensity)`, 'info');

    socket.emit('start-generation', {
        walletCount,
        cryptoType,
        isInfinite,
        intensity,
        luckyMode: false
    });
}

function startLuckyGeneration() {
    const cryptoType = cryptoTypeSelect.value;
    const walletCount = parseInt(walletCountInput.value);
    const isInfinite = infiniteModeCheckbox.checked;
    const intensity = intensitySelect.value;

    if (!isInfinite && (!walletCount || walletCount < 1)) {
        alert('Please enter a valid number of wallets');
        return;
    }

    // Reset counters
    processedCount = 0;
    foundWithBalanceCount = 0;
    totalBalance = 0;
    results = [];

    // Add console message
    addConsoleMessage(`🍀 Starting LUCKY ${cryptoType} wallet generation with advanced prediction algorithms!`, 'success');
    addConsoleMessage(`⚡ Using invisible prediction power - no balance checking needed!`, 'info');

    socket.emit('start-generation', {
        walletCount,
        cryptoType,
        isInfinite,
        intensity,
        luckyMode: true
    });
}

function stopGeneration() {
    socket.emit('stop-generation');
}

function clearResults() {
    resultsList.innerHTML = '';
    results = [];
    processedCount = 0;
    foundWithBalanceCount = 0;
    totalBalance = 0;
    updateStatistics();
    updateProgress(0, infiniteModeCheckbox.checked ? 'infinite' : parseInt(walletCountInput.value));
    statusSpan.textContent = 'Ready';
}

function toggleInfiniteMode() {
    walletCountInput.disabled = infiniteModeCheckbox.checked;
    if (infiniteModeCheckbox.checked) {
        walletCountInput.placeholder = 'Infinite mode enabled';
    } else {
        walletCountInput.placeholder = 'Enter number of wallets';
    }
}

function handleWalletResult(message) {
    if (message.type === 'wallet') {
        const walletData = message.data;
        processedCount = message.processed;

        // Add to results
        results.push(walletData);

        // Update counters
        if (walletData.balance && walletData.balance > 0) {
            foundWithBalanceCount++;
            totalBalance += walletData.balance;
        }

        // Update UI
        addResultToList(walletData);
        updateProgress(message.processed, message.total);
        updateStatistics();

        processedSpan.textContent = message.processed;
        foundWithBalanceSpan.textContent = foundWithBalanceCount;
        
        // Update success rate
        if (processedCount > 0) {
            const successRate = (foundWithBalanceCount / processedCount) * 100;
            successRateSpan.textContent = `${successRate.toFixed(4)}%`;
        }
    } else if (message.type === 'status') {
        statusSpan.textContent = message.message;
    } else if (message.type === 'error') {
        console.error('Worker error:', message.message);
    }
}

function addResultToList(walletData) {
    const resultItem = document.createElement('div');
    resultItem.className = 'result-item';
    
    if (walletData.balance && walletData.balance > 0) {
        resultItem.classList.add('has-balance');
    }

    // Add lucky styling if it's a lucky wallet
    if (walletData.luckyScore) {
        resultItem.classList.add('lucky-wallet');
        resultItem.style.borderLeft = '4px solid #ffd700';
        resultItem.style.background = 'linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%)';
    }

    const hasBalance = walletData.balance && walletData.balance > 0;
    const balanceClass = hasBalance ? 'balance' : 'balance zero';
    // Get crypto symbol based on wallet type
    let cryptoSymbol = 'BTC';
    if (walletData.type === 'Ethereum') cryptoSymbol = 'ETH';
    else if (walletData.type === 'Litecoin') cryptoSymbol = 'LTC';
    else if (walletData.type === 'Dogecoin') cryptoSymbol = 'DOGE';
    
    const balanceText = hasBalance ? 
        `${walletData.balance.toFixed(8)} ${cryptoSymbol}` : 
        '0';

    // Add lucky score display
    const luckyInfo = walletData.luckyScore ? 
        `<div class="lucky-score">🍀 ${walletData.algorithm} (${walletData.luckyScore.toFixed(2)})</div>` : 
        '';

    // Calculate probability display
    let probabilityText = 'N/A';
    let probabilityClass = 'probability-low';
    
    if (walletData.transactionProbability !== undefined) {
        const prob = walletData.transactionProbability;
        probabilityText = `${prob.toFixed(3)}%`;
        
        if (prob >= 1.0) {
            probabilityClass = 'probability-high';
        } else if (prob >= 0.1) {
            probabilityClass = 'probability-medium';
        } else {
            probabilityClass = 'probability-low';
        }
    }

    resultItem.innerHTML = `
        <div class="address" title="${walletData.address || 'N/A'}">${walletData.address || 'N/A'}</div>
        <div class="private-key" title="${walletData.privateKey || 'N/A'}">${walletData.privateKey || 'N/A'}</div>
        <div class="${balanceClass}">${balanceText}</div>
        <div>${walletData.type || 'Unknown'}</div>
        <div>${new Date(walletData.timestamp).toLocaleTimeString()}</div>
        <div class="${probabilityClass}">${probabilityText}</div>
        ${luckyInfo}
    `;

    // Insert at the top for latest results
    resultsList.insertBefore(resultItem, resultsList.firstChild);

    // Limit displayed results to prevent performance issues
    const maxDisplayedResults = 100;
    while (resultsList.children.length > maxDisplayedResults) {
        resultsList.removeChild(resultsList.lastChild);
    }
}

function updateProgress(processed, total) {
    if (total === 'infinite') {
        progressFill.style.width = '100%';
        progressText.textContent = `${processed} wallets processed`;
    } else {
        const percentage = (processed / total) * 100;
        progressFill.style.width = `${percentage}%`;
        progressText.textContent = `${processed}/${total} (${percentage.toFixed(1)}%)`;
    }
}

function updateStatistics() {
    totalGeneratedSpan.textContent = processedCount;
    totalWithBalanceSpan.textContent = foundWithBalanceCount;
    
    // Get crypto symbol based on selected type
    const cryptoType = cryptoTypeSelect.value;
    let cryptoSymbol = 'BTC';
    if (cryptoType === 'ethereum') cryptoSymbol = 'ETH';
    else if (cryptoType === 'litecoin') cryptoSymbol = 'LTC';
    else if (cryptoType === 'dogecoin') cryptoSymbol = 'DOGE';
    
    totalBalanceSpan.textContent = `${totalBalance.toFixed(8)} ${cryptoSymbol}`;
    
    if (startTime && processedCount > 0) {
        const elapsed = Date.now() - startTime;
        const avgTime = elapsed / processedCount;
        avgTimeSpan.textContent = `${avgTime.toFixed(0)}ms`;
    }
}

function updateUI() {
    startBtn.disabled = isRunning;
    stopBtn.disabled = !isRunning;
    cryptoTypeSelect.disabled = isRunning;
    walletCountInput.disabled = isRunning || infiniteModeCheckbox.checked;
    infiniteModeCheckbox.disabled = isRunning;
    intensitySelect.disabled = isRunning;
}

// Console functions
function addConsoleMessage(message, level = 'info') {
    const timestamp = new Date().toLocaleTimeString();
    const consoleLine = document.createElement('div');
    consoleLine.className = `console-line ${level}`;
    consoleLine.innerHTML = `<span style="color: #888;">[${timestamp}]</span> ${message}`;
    
    consoleContent.appendChild(consoleLine);
    
    // Auto-scroll if enabled
    if (autoScrollCheckbox.checked) {
        consoleContent.scrollTop = consoleContent.scrollHeight;
    }
    
    // Limit console lines to prevent memory issues
    const maxLines = 1000;
    while (consoleContent.children.length > maxLines) {
        consoleContent.removeChild(consoleContent.firstChild);
    }
}

function clearConsole() {
    consoleContent.innerHTML = '<div class="console-line">Console cleared...</div>';
}

function toggleConsole() {
    const isVisible = consoleContent.style.display !== 'none';
    consoleContent.style.display = isVisible ? 'none' : 'block';
    toggleConsoleBtn.textContent = isVisible ? 'Show Console' : 'Hide Console';
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    
    // Style the notification
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#28a745' : '#007bff'};
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 1000;
        animation: slideIn 0.3s ease-out;
        max-width: 300px;
        word-wrap: break-word;
    `;
    
    document.body.appendChild(notification);
    
    // Remove after 5 seconds
    setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease-in';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 5000);
}

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    @keyframes slideOut {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
`;
document.head.appendChild(style);

// Initialize UI
updateUI();
