<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Crypto Wallet Generator</title>
    <link rel="stylesheet" href="style.css">
    <script src="/socket.io/socket.io.js"></script>
</head>
<body>
    <div class="container">
        <header>
            <h1>🔐 Crypto Wallet Generator</h1>
            <p>Generate random crypto wallets and check their balances</p>
        </header>

        <div class="controls">
            <div class="input-group">
                <label for="cryptoType">Cryptocurrency:</label>
                <select id="cryptoType">
                    <option value="bitcoin">Bitcoin (BTC)</option>
                    <option value="ethereum">Ethereum (ETH)</option>
                    <option value="litecoin">Litecoin (LTC)</option>
                    <option value="dogecoin">Dogecoin (DOGE)</option>
                </select>
            </div>

            <div class="input-group">
                <label for="walletCount">Number of wallets:</label>
                <input type="number" id="walletCount" min="1" max="1000" value="10" placeholder="Enter number of wallets">
            </div>

            <div class="input-group">
                <label>
                    <input type="checkbox" id="infiniteMode"> Infinite mode (run until stopped)
                </label>
            </div>

            <div class="input-group">
                <label for="intensity">Generation Intensity:</label>
                <select id="intensity">
                    <option value="normal">Normal (30 concurrent)</option>
                    <option value="aggressive" selected>Aggressive (80 concurrent)</option>
                    <option value="extreme">Extreme (150 concurrent)</option>
                </select>
            </div>

            <div class="input-group lucky-section">
                <label>
                    <input type="checkbox" id="luckyMode"> 🍀 I Feel Lucky Today! (Advanced Prediction Mode)
                </label>
                <div class="lucky-description">
                    Uses invisible algorithms to predict wallets most likely to have transaction history. 
                    No balance checking - pure prediction power! ⚡
                </div>
            </div>

            <div class="button-group">
                <button id="startBtn" class="btn btn-primary">Start Generation</button>
                <button id="luckyBtn" class="btn btn-lucky">🍀 I Feel Lucky!</button>
                <button id="stopBtn" class="btn btn-danger" disabled>Stop</button>
                <button id="clearBtn" class="btn btn-secondary">Clear Results</button>
            </div>
        </div>

        <div class="status">
            <div class="status-item">
                <span class="label">Status:</span>
                <span id="status" class="value">Ready</span>
            </div>
            <div class="status-item">
                <span class="label">Processed:</span>
                <span id="processed" class="value">0</span>
            </div>
            <div class="status-item">
                <span class="label">Found with Balance:</span>
                <span id="foundWithBalance" class="value">0</span>
            </div>
            <div class="status-item">
                <span class="label">Success Rate:</span>
                <span id="successRate" class="value">0%</span>
            </div>
        </div>

        <div class="features">
            <h3>🎯 Enhanced Features</h3>
            <div class="features-grid">
                <div class="feature-item">
                    <div class="feature-icon">📅</div>
                    <div class="feature-text">Historical Time Targeting</div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">🔍</div>
                    <div class="feature-text">Smart Address Patterns</div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">⚡</div>
                    <div class="feature-text">Bulk Balance Checking (10x faster)</div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">🎲</div>
                    <div class="feature-text">4 Cryptocurrencies Supported</div>
                </div>
            </div>
        </div>

        <div class="progress-container">
            <div class="progress-bar">
                <div id="progressFill" class="progress-fill"></div>
            </div>
            <div id="progressText" class="progress-text">0%</div>
        </div>

        <div class="high-probability-summary" id="highProbabilitySummary" style="display: none;">
            <h3>🔥 High Probability Wallets Found</h3>
            <div class="summary-description">
                These wallets have been identified by our prediction algorithms as having the highest likelihood of transaction history:
            </div>
            <div id="highProbabilityList" class="high-probability-list">
                <!-- High probability wallets will be shown here -->
            </div>
        </div>

        <div class="results">
            <div class="results-controls">
                <h2>Results</h2>
                <div class="filter-controls">
                    <label for="probabilityFilter">Filter by Probability:</label>
                    <select id="probabilityFilter">
                        <option value="all">All Wallets</option>
                        <option value="high">High Probability (≥1%)</option>
                        <option value="medium">Medium+ Probability (≥0.5%)</option>
                        <option value="low">Low+ Probability (≥0.1%)</option>
                    </select>
                    <button id="sortByProbability" class="btn btn-secondary">Sort by Probability</button>
                </div>
            </div>
            <div class="results-header">
                <span>Address</span>
                <span>Private Key</span>
                <span>Balance</span>
                <span>Type</span>
                <span>Time</span>
                <span>🎯 Transaction Probability</span>
            </div>
            <div id="resultsList" class="results-list">
                <!-- Results will be populated here -->
            </div>
        </div>

        <div class="stats">
            <h3>Statistics</h3>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-value" id="totalGenerated">0</div>
                    <div class="stat-label">Total Generated</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="totalWithBalance">0</div>
                    <div class="stat-label">With Balance</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="totalBalance">0</div>
                    <div class="stat-label">Total Balance</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="avgTime">0ms</div>
                    <div class="stat-label">Avg Time/Wallet</div>
                </div>
            </div>
        </div>

        <div class="probability-stats">
            <h3>🎯 Probability Distribution</h3>
            <div class="probability-stats-grid">
                <div class="probability-stat-item very-high">
                    <div class="probability-stat-value" id="veryHighProb">0</div>
                    <div class="probability-stat-label">🔥 Very High (≥5%)</div>
                </div>
                <div class="probability-stat-item high">
                    <div class="probability-stat-value" id="highProb">0</div>
                    <div class="probability-stat-label">⭐ High (≥1%)</div>
                </div>
                <div class="probability-stat-item medium">
                    <div class="probability-stat-value" id="mediumProb">0</div>
                    <div class="probability-stat-label">🎯 Medium (≥0.5%)</div>
                </div>
                <div class="probability-stat-item low">
                    <div class="probability-stat-value" id="lowProb">0</div>
                    <div class="probability-stat-label">📊 Low (≥0.1%)</div>
                </div>
                <div class="probability-stat-item very-low">
                    <div class="probability-stat-value" id="veryLowProb">0</div>
                    <div class="probability-stat-label">🔍 Very Low (<0.1%)</div>
                </div>
            </div>
        </div>

        <div class="console-output">
            <h3>🔍 API Console Output</h3>
            <div class="console-controls">
                <button id="clearConsole" class="btn btn-secondary">Clear Console</button>
                <button id="toggleConsole" class="btn btn-secondary">Toggle Console</button>
                <label>
                    <input type="checkbox" id="autoScroll" checked> Auto-scroll
                </label>
            </div>
            <div id="consoleContent" class="console-content">
                <div class="console-line">Console ready. API responses will appear here...</div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
