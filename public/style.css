* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

header p {
    font-size: 1.1rem;
    opacity: 0.9;
}

.controls {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.input-group {
    margin-bottom: 20px;
}

.input-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #555;
}

.input-group input,
.input-group select {
    width: 100%;
    padding: 12px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s;
}

.input-group input:focus,
.input-group select:focus {
    outline: none;
    border-color: #667eea;
}

.input-group input[type="checkbox"] {
    width: auto;
    margin-right: 10px;
}

.button-group {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.btn-danger {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    color: white;
}

.btn-danger:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 107, 107, 0.4);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover:not(:disabled) {
    background: #5a6268;
    transform: translateY(-2px);
}

.btn-lucky {
    background: linear-gradient(135deg, #ff6b6b 0%, #feca57 50%, #48dbfb 100%);
    color: white;
    animation: luckyGlow 2s ease-in-out infinite alternate;
}

.btn-lucky:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 5px 20px rgba(255, 107, 107, 0.4);
    animation: luckyGlow 1s ease-in-out infinite alternate;
}

@keyframes luckyGlow {
    from { box-shadow: 0 0 10px rgba(255, 107, 107, 0.3); }
    to { box-shadow: 0 0 20px rgba(255, 107, 107, 0.6); }
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(40, 167, 69, 0); }
    100% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0); }
}

@keyframes glow {
    from {
        box-shadow: 0 0 20px rgba(255, 71, 87, 0.3);
    }
    to {
        box-shadow: 0 0 30px rgba(255, 71, 87, 0.6), 0 0 40px rgba(255, 71, 87, 0.3);
    }
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.lucky-section {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border: 2px solid #ffd700;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 20px;
}

.lucky-description {
    font-size: 12px;
    color: #856404;
    margin-top: 8px;
    font-style: italic;
}

.status {
    background: white;
    padding: 20px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    margin-bottom: 20px;
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
    gap: 20px;
}

.status-item {
    text-align: center;
}

.status-item .label {
    display: block;
    font-size: 14px;
    color: #666;
    margin-bottom: 5px;
}

.status-item .value {
    display: block;
    font-size: 18px;
    font-weight: bold;
    color: #333;
}

.features {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.features h3 {
    margin-bottom: 20px;
    color: #333;
    text-align: center;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.feature-item {
    text-align: center;
    padding: 20px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 10px;
    transition: transform 0.3s;
}

.feature-item:hover {
    transform: translateY(-2px);
}

.feature-icon {
    font-size: 2rem;
    margin-bottom: 10px;
}

.feature-text {
    font-size: 14px;
    color: #666;
    font-weight: 600;
}

.console-output {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    margin-bottom: 20px;
    overflow: hidden;
}

.console-output h3 {
    padding: 20px;
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
    margin: 0;
}

.console-controls {
    padding: 15px 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
}

.console-controls label {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 14px;
    color: #666;
}

.console-content {
    max-height: 300px;
    overflow-y: auto;
    background: #1e1e1e;
    color: #f8f8f2;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    padding: 10px;
}

.console-line {
    margin-bottom: 5px;
    word-wrap: break-word;
    line-height: 1.4;
}

.console-line.info {
    color: #87ceeb;
}

.console-line.success {
    color: #98fb98;
}

.console-line.warning {
    color: #ffd700;
}

.console-line.error {
    color: #ff6b6b;
}

.console-line.api-request {
    color: #dda0dd;
    border-left: 3px solid #dda0dd;
    padding-left: 10px;
    margin-left: 5px;
}

.console-line.api-response {
    color: #90ee90;
    border-left: 3px solid #90ee90;
    padding-left: 10px;
    margin-left: 5px;
}

.console-line.bulk-check {
    color: #ffa500;
    border-left: 3px solid #ffa500;
    padding-left: 10px;
    margin-left: 5px;
}

.progress-container {
    background: white;
    padding: 20px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    width: 0%;
    transition: width 0.3s ease;
}

.progress-text {
    text-align: center;
    font-weight: bold;
    color: #555;
}

/* High Probability Summary */
.high-probability-summary {
    background: linear-gradient(135deg, #ff6b6b 0%, #feca57 100%);
    color: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(255, 107, 107, 0.3);
    margin-bottom: 20px;
    animation: glow 2s ease-in-out infinite alternate;
}

.high-probability-summary h3 {
    margin: 0 0 15px 0;
    font-size: 1.5rem;
    text-align: center;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.summary-description {
    text-align: center;
    margin-bottom: 20px;
    font-size: 1.1rem;
    opacity: 0.9;
}

.high-probability-list {
    display: grid;
    gap: 10px;
}

.high-probability-item {
    background: rgba(255, 255, 255, 0.2);
    padding: 15px;
    border-radius: 10px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    display: grid;
    grid-template-columns: 2fr 2fr 1fr 1fr;
    gap: 15px;
    align-items: center;
}

.high-probability-item .address {
    font-family: monospace;
    font-size: 12px;
    word-break: break-all;
    font-weight: bold;
}

.high-probability-item .private-key {
    font-family: monospace;
    font-size: 12px;
    word-break: break-all;
}

.high-probability-item .probability {
    font-size: 16px;
    font-weight: bold;
    text-align: center;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

.high-probability-item .crypto-type {
    text-align: center;
    font-weight: bold;
}

.results {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    margin-bottom: 20px;
    overflow: hidden;
}

.results h2 {
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    margin: 0;
}

.results-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    flex-wrap: wrap;
    gap: 15px;
}

.results-controls h2 {
    margin: 0;
    padding: 0;
    background: none;
    color: #333;
    font-size: 1.5rem;
}

.filter-controls {
    display: flex;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;
}

.filter-controls label {
    font-weight: 600;
    color: #555;
    font-size: 14px;
}

.filter-controls select {
    padding: 8px 12px;
    border: 2px solid #e1e5e9;
    border-radius: 6px;
    font-size: 14px;
    background: white;
    min-width: 150px;
}

.filter-controls select:focus {
    outline: none;
    border-color: #667eea;
}

.results-header {
    display: grid;
    grid-template-columns: 2fr 2fr 1fr 1fr 1fr 1fr;
    gap: 10px;
    padding: 15px 20px;
    background: #f8f9fa;
    font-weight: bold;
    border-bottom: 1px solid #dee2e6;
}

.results-list {
    max-height: 400px;
    overflow-y: auto;
}

.result-item {
    display: grid;
    grid-template-columns: 2fr 2fr 1fr 1fr 1fr 1fr;
    gap: 10px;
    padding: 15px 20px;
    border-bottom: 1px solid #f1f3f4;
    transition: background-color 0.2s;
}

.result-item:hover {
    background-color: #f8f9fa;
}

.result-item.has-balance {
    background-color: #d4edda;
    border-left: 4px solid #28a745;
    animation: pulse 2s infinite;
}

.result-item.lucky-wallet {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%) !important;
    border-left: 4px solid #ffd700 !important;
    position: relative;
}

/* Probability-based styling for high-probability wallets */
.result-item.probability-very-high {
    background: linear-gradient(135deg, #ff6b6b 0%, #feca57 100%) !important;
    border-left: 5px solid #ff4757 !important;
    animation: glow 2s ease-in-out infinite alternate;
    box-shadow: 0 0 20px rgba(255, 71, 87, 0.3);
    transform: scale(1.02);
}

.result-item.probability-high {
    background: linear-gradient(135deg, #ffeaa7 0%, #fdcb6e 100%) !important;
    border-left: 5px solid #e17055 !important;
    animation: shimmer 3s ease-in-out infinite;
    box-shadow: 0 0 15px rgba(225, 112, 85, 0.2);
}

.result-item.probability-medium {
    background: linear-gradient(135deg, #a8e6cf 0%, #88d8a3 100%) !important;
    border-left: 5px solid #00b894 !important;
    box-shadow: 0 0 10px rgba(0, 184, 148, 0.15);
}

.result-item.probability-low {
    background: linear-gradient(135deg, #ddd6fe 0%, #c4b5fd 100%) !important;
    border-left: 5px solid #8b5cf6 !important;
}

.result-item.probability-very-low {
    background: linear-gradient(135deg, #f1f3f4 0%, #e8eaed 100%) !important;
    border-left: 5px solid #9aa0a6 !important;
}

.lucky-score {
    position: absolute;
    top: 5px;
    right: 10px;
    background: rgba(255, 215, 0, 0.9);
    color: #856404;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: bold;
}

/* Enhanced probability percentage styling */
.probability-very-high {
    color: #ff4757 !important;
    font-weight: bold !important;
    font-size: 14px !important;
    text-shadow: 0 0 5px rgba(255, 71, 87, 0.5);
    animation: glow 2s ease-in-out infinite alternate;
}

.probability-high {
    color: #e17055 !important;
    font-weight: bold !important;
    font-size: 13px !important;
    text-shadow: 0 0 3px rgba(225, 112, 85, 0.3);
}

.probability-medium {
    color: #00b894 !important;
    font-weight: bold !important;
    font-size: 12px !important;
}

.probability-low {
    color: #8b5cf6 !important;
    font-weight: normal !important;
}

.probability-very-low {
    color: #9aa0a6 !important;
    font-weight: normal !important;
}

.result-item .address,
.result-item .private-key {
    font-family: monospace;
    font-size: 12px;
    word-break: break-all;
}

.result-item .balance {
    font-weight: bold;
    color: #28a745;
}

.result-item .balance.zero {
    color: #6c757d;
}

.stats {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.stats h3 {
    margin-bottom: 20px;
    color: #333;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.stat-item {
    text-align: center;
    padding: 20px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 10px;
}

.stat-value {
    font-size: 2rem;
    font-weight: bold;
    color: #667eea;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 14px;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Probability Statistics */
.probability-stats {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.probability-stats h3 {
    text-align: center;
    margin-bottom: 20px;
    color: #333;
    font-size: 1.5rem;
}

.probability-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.probability-stat-item {
    text-align: center;
    padding: 20px;
    border-radius: 12px;
    transition: transform 0.3s ease;
}

.probability-stat-item:hover {
    transform: translateY(-5px);
}

.probability-stat-item.very-high {
    background: linear-gradient(135deg, #ff6b6b 0%, #feca57 100%);
    color: white;
    box-shadow: 0 5px 15px rgba(255, 107, 107, 0.3);
}

.probability-stat-item.high {
    background: linear-gradient(135deg, #ffeaa7 0%, #fdcb6e 100%);
    color: #333;
    box-shadow: 0 5px 15px rgba(253, 203, 110, 0.3);
}

.probability-stat-item.medium {
    background: linear-gradient(135deg, #a8e6cf 0%, #88d8a3 100%);
    color: white;
    box-shadow: 0 5px 15px rgba(136, 216, 163, 0.3);
}

.probability-stat-item.low {
    background: linear-gradient(135deg, #ddd6fe 0%, #c4b5fd 100%);
    color: #333;
    box-shadow: 0 5px 15px rgba(196, 181, 253, 0.3);
}

.probability-stat-item.very-low {
    background: linear-gradient(135deg, #f1f3f4 0%, #e8eaed 100%);
    color: #666;
    box-shadow: 0 5px 15px rgba(241, 243, 244, 0.3);
}

.probability-stat-value {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.probability-stat-label {
    font-size: 14px;
    font-weight: 600;
}

@media (max-width: 768px) {
    .container {
        padding: 10px;
    }

    .results-header,
    .result-item {
        grid-template-columns: 1fr;
        gap: 5px;
    }

    .result-item .address,
    .result-item .private-key {
        font-size: 10px;
    }

    .button-group {
        flex-direction: column;
    }

    .status {
        flex-direction: column;
        gap: 10px;
    }
}
