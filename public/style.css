* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

header p {
    font-size: 1.1rem;
    opacity: 0.9;
}

.controls {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.input-group {
    margin-bottom: 20px;
}

.input-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #555;
}

.input-group input,
.input-group select {
    width: 100%;
    padding: 12px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s;
}

.input-group input:focus,
.input-group select:focus {
    outline: none;
    border-color: #667eea;
}

.input-group input[type="checkbox"] {
    width: auto;
    margin-right: 10px;
}

.button-group {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.btn-danger {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    color: white;
}

.btn-danger:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 107, 107, 0.4);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover:not(:disabled) {
    background: #5a6268;
    transform: translateY(-2px);
}

.btn-lucky {
    background: linear-gradient(135deg, #ff6b6b 0%, #feca57 50%, #48dbfb 100%);
    color: white;
    animation: luckyGlow 2s ease-in-out infinite alternate;
}

.btn-lucky:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 5px 20px rgba(255, 107, 107, 0.4);
    animation: luckyGlow 1s ease-in-out infinite alternate;
}

@keyframes luckyGlow {
    from { box-shadow: 0 0 10px rgba(255, 107, 107, 0.3); }
    to { box-shadow: 0 0 20px rgba(255, 107, 107, 0.6); }
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(40, 167, 69, 0); }
    100% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0); }
}

@keyframes glow {
    from {
        box-shadow: 0 0 20px rgba(255, 71, 87, 0.3);
    }
    to {
        box-shadow: 0 0 30px rgba(255, 71, 87, 0.6), 0 0 40px rgba(255, 71, 87, 0.3);
    }
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.lucky-section {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border: 2px solid #ffd700;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 20px;
}

.lucky-description {
    font-size: 12px;
    color: #856404;
    margin-top: 8px;
    font-style: italic;
}

.status {
    background: white;
    padding: 20px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    margin-bottom: 20px;
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
    gap: 20px;
}

.status-item {
    text-align: center;
}

.status-item .label {
    display: block;
    font-size: 14px;
    color: #666;
    margin-bottom: 5px;
}

.status-item .value {
    display: block;
    font-size: 18px;
    font-weight: bold;
    color: #333;
}

.features {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.features h3 {
    margin-bottom: 20px;
    color: #333;
    text-align: center;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.feature-item {
    text-align: center;
    padding: 20px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 10px;
    transition: transform 0.3s;
}

.feature-item:hover {
    transform: translateY(-2px);
}

.feature-icon {
    font-size: 2rem;
    margin-bottom: 10px;
}

.feature-text {
    font-size: 14px;
    color: #666;
    font-weight: 600;
}

.console-output {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    margin-bottom: 20px;
    overflow: hidden;
}

.console-output h3 {
    padding: 20px;
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
    margin: 0;
}

.console-controls {
    padding: 15px 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
}

.console-controls label {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 14px;
    color: #666;
}

.console-content {
    max-height: 300px;
    overflow-y: auto;
    background: #1e1e1e;
    color: #f8f8f2;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    padding: 10px;
}

.console-line {
    margin-bottom: 5px;
    word-wrap: break-word;
    line-height: 1.4;
}

.console-line.info {
    color: #87ceeb;
}

.console-line.success {
    color: #98fb98;
}

.console-line.warning {
    color: #ffd700;
}

.console-line.error {
    color: #ff6b6b;
}

.console-line.api-request {
    color: #dda0dd;
    border-left: 3px solid #dda0dd;
    padding-left: 10px;
    margin-left: 5px;
}

.console-line.api-response {
    color: #90ee90;
    border-left: 3px solid #90ee90;
    padding-left: 10px;
    margin-left: 5px;
}

.console-line.bulk-check {
    color: #ffa500;
    border-left: 3px solid #ffa500;
    padding-left: 10px;
    margin-left: 5px;
}

.progress-container {
    background: white;
    padding: 20px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    width: 0%;
    transition: width 0.3s ease;
}

.progress-text {
    text-align: center;
    font-weight: bold;
    color: #555;
}

/* High Probability Summary - IMPROVED CONTRAST */
.high-probability-summary {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    color: #1f2937;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 3px solid #dc2626;
    margin-bottom: 20px;
}

.high-probability-summary h3 {
    margin: 0 0 15px 0;
    font-size: 1.5rem;
    text-align: center;
    color: #dc2626;
    font-weight: bold;
}

.summary-description {
    text-align: center;
    margin-bottom: 20px;
    font-size: 1.1rem;
    color: #374151;
    font-weight: 500;
}

.high-probability-list {
    display: grid;
    gap: 10px;
}

.high-probability-item {
    background: #ffffff;
    padding: 15px;
    border-radius: 10px;
    border: 2px solid #dc2626;
    display: grid;
    grid-template-columns: 2fr 2fr 1fr 1fr;
    gap: 15px;
    align-items: center;
    box-shadow: 0 2px 8px rgba(220, 38, 38, 0.1);
}

.high-probability-item .address {
    font-family: monospace;
    font-size: 12px;
    word-break: break-all;
    font-weight: bold;
    color: #1f2937;
}

.high-probability-item .private-key {
    font-family: monospace;
    font-size: 12px;
    word-break: break-all;
    color: #374151;
}

.high-probability-item .probability {
    font-size: 16px;
    font-weight: bold;
    text-align: center;
    color: #dc2626;
    background: rgba(220, 38, 38, 0.1);
    padding: 8px;
    border-radius: 6px;
}

.high-probability-item .crypto-type {
    text-align: center;
    font-weight: bold;
    color: #059669;
}

.results {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    margin-bottom: 20px;
    overflow: hidden;
}

.results h2 {
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    margin: 0;
}

.results-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    flex-wrap: wrap;
    gap: 15px;
}

.results-controls h2 {
    margin: 0;
    padding: 0;
    background: none;
    color: #333;
    font-size: 1.5rem;
}

.filter-controls {
    display: flex;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;
}

.filter-controls label {
    font-weight: 600;
    color: #555;
    font-size: 14px;
}

.filter-controls select {
    padding: 8px 12px;
    border: 2px solid #e1e5e9;
    border-radius: 6px;
    font-size: 14px;
    background: white;
    min-width: 150px;
}

.filter-controls select:focus {
    outline: none;
    border-color: #667eea;
}

.results-header {
    display: grid;
    grid-template-columns: 2fr 2fr 1fr 1fr 1fr 1.5fr 1fr;
    gap: 10px;
    padding: 15px 20px;
    background: #f8f9fa;
    font-weight: bold;
    border-bottom: 1px solid #dee2e6;
}

.results-list {
    max-height: 400px;
    overflow-y: auto;
}

.result-item {
    display: grid;
    grid-template-columns: 2fr 2fr 1fr 1fr 1fr 1.5fr 1fr;
    gap: 10px;
    padding: 15px 20px;
    border-bottom: 1px solid #f1f3f4;
    transition: background-color 0.2s;
    align-items: center;
}

.result-item:hover {
    background-color: #f8f9fa;
}

.result-item.has-balance {
    background-color: #d4edda;
    border-left: 4px solid #28a745;
    animation: pulse 2s infinite;
}

.result-item.lucky-wallet {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%) !important;
    border-left: 4px solid #ffd700 !important;
    position: relative;
}

/* Probability-based styling for high-probability wallets - IMPROVED CONTRAST */
.result-item.probability-very-high {
    background: linear-gradient(135deg, #ffffff 0%, #fff5f5 100%) !important;
    border-left: 5px solid #dc2626 !important;
    border: 2px solid #dc2626 !important;
    color: #1f2937 !important;
    animation: glow 2s ease-in-out infinite alternate;
    box-shadow: 0 0 20px rgba(220, 38, 38, 0.2);
}

.result-item.probability-high {
    background: linear-gradient(135deg, #ffffff 0%, #fffbeb 100%) !important;
    border-left: 5px solid #d97706 !important;
    border: 2px solid #d97706 !important;
    color: #1f2937 !important;
    box-shadow: 0 0 15px rgba(217, 119, 6, 0.15);
}

.result-item.probability-medium {
    background: linear-gradient(135deg, #ffffff 0%, #f0fdf4 100%) !important;
    border-left: 5px solid #059669 !important;
    border: 2px solid #059669 !important;
    color: #1f2937 !important;
    box-shadow: 0 0 10px rgba(5, 150, 105, 0.1);
}

.result-item.probability-low {
    background: linear-gradient(135deg, #ffffff 0%, #faf5ff 100%) !important;
    border-left: 5px solid #7c3aed !important;
    border: 1px solid #7c3aed !important;
    color: #1f2937 !important;
}

.result-item.probability-very-low {
    background: linear-gradient(135deg, #ffffff 0%, #f9fafb 100%) !important;
    border-left: 5px solid #6b7280 !important;
    border: 1px solid #e5e7eb !important;
    color: #374151 !important;
}

.lucky-score {
    position: absolute;
    top: 5px;
    right: 10px;
    background: rgba(255, 215, 0, 0.9);
    color: #856404;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: bold;
}

/* ADVANCED PROBABILITY STYLING - SOPHISTICATED MATHEMATICAL CLASSIFICATION */

/* Exceptional - 15%+ (Diamond Tier) */
.probability-exceptional {
    color: #7c3aed !important;
    font-weight: bold !important;
    font-size: 16px !important;
    background: linear-gradient(135deg, #a855f7, #ec4899) !important;
    color: white !important;
    padding: 6px 12px !important;
    border-radius: 8px !important;
    border: 2px solid #7c3aed !important;
    box-shadow: 0 4px 12px rgba(124, 58, 237, 0.4) !important;
    animation: pulse 2s infinite !important;
}

/* Legendary - 10-15% (Star Tier) */
.probability-legendary {
    color: #f59e0b !important;
    font-weight: bold !important;
    font-size: 15px !important;
    background: linear-gradient(135deg, #fbbf24, #f59e0b) !important;
    color: white !important;
    padding: 6px 12px !important;
    border-radius: 8px !important;
    border: 2px solid #f59e0b !important;
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.4) !important;
    animation: glow 3s ease-in-out infinite alternate !important;
}

/* Very High - 5-10% (Fire Tier) */
.probability-very-high {
    color: #dc2626 !important;
    font-weight: bold !important;
    font-size: 14px !important;
    background: rgba(220, 38, 38, 0.15) !important;
    padding: 5px 10px !important;
    border-radius: 7px !important;
    border: 2px solid #dc2626 !important;
    box-shadow: 0 2px 8px rgba(220, 38, 38, 0.3) !important;
}

/* High - 2-5% (Star Tier) */
.probability-high {
    color: #d97706 !important;
    font-weight: bold !important;
    font-size: 13px !important;
    background: rgba(217, 119, 6, 0.15) !important;
    padding: 5px 10px !important;
    border-radius: 7px !important;
    border: 2px solid #d97706 !important;
    box-shadow: 0 2px 8px rgba(217, 119, 6, 0.3) !important;
}

/* Medium-High - 1-2% (Target Tier) */
.probability-medium-high {
    color: #ea580c !important;
    font-weight: bold !important;
    font-size: 13px !important;
    background: rgba(234, 88, 12, 0.12) !important;
    padding: 5px 10px !important;
    border-radius: 6px !important;
    border: 2px solid #ea580c !important;
    box-shadow: 0 2px 6px rgba(234, 88, 12, 0.25) !important;
}

/* Medium - 0.5-1% (Sparkle Tier) */
.probability-medium {
    color: #059669 !important;
    font-weight: bold !important;
    font-size: 12px !important;
    background: rgba(5, 150, 105, 0.12) !important;
    padding: 4px 8px !important;
    border-radius: 6px !important;
    border: 1px solid #059669 !important;
    box-shadow: 0 1px 4px rgba(5, 150, 105, 0.2) !important;
}

/* Low - 0.1-0.5% (Search Tier) */
.probability-low {
    color: #7c3aed !important;
    font-weight: bold !important;
    background: rgba(124, 58, 237, 0.1) !important;
    padding: 4px 8px !important;
    border-radius: 6px !important;
    border: 1px solid #7c3aed !important;
}

/* Very Low - <0.1% (Common Tier) */
.probability-very-low {
    color: #6b7280 !important;
    font-weight: normal !important;
    background: rgba(107, 114, 128, 0.08) !important;
    padding: 4px 8px !important;
    border-radius: 6px !important;
    border: 1px solid #d1d5db !important;
}

/* Animation keyframes for exceptional probabilities */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes glow {
    from { box-shadow: 0 4px 12px rgba(245, 158, 11, 0.4); }
    to { box-shadow: 0 6px 20px rgba(245, 158, 11, 0.6); }
}

/* ADVANCED ALGORITHM ANALYSIS PANEL */
.algorithm-panel {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border: 2px solid #3b82f6;
    border-radius: 12px;
    padding: 25px;
    margin: 20px 0;
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
}

.algorithm-panel h3 {
    color: #1e40af;
    font-size: 1.5rem;
    margin-bottom: 20px;
    text-align: center;
    font-weight: bold;
}

.algorithm-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 25px;
}

.algorithm-section {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.algorithm-section h4 {
    color: #374151;
    font-size: 1.1rem;
    margin-bottom: 15px;
    font-weight: 600;
    border-bottom: 2px solid #e5e7eb;
    padding-bottom: 8px;
}

.metrics-display {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.metric {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: #f9fafb;
    border-radius: 6px;
    border-left: 3px solid #3b82f6;
}

.metric-label {
    font-weight: 500;
    color: #374151;
    font-size: 0.9rem;
}

.metric-value {
    font-weight: bold;
    color: #1f2937;
    font-family: 'Courier New', monospace;
    background: #e5e7eb;
    padding: 2px 8px;
    border-radius: 4px;
    min-width: 60px;
    text-align: center;
}

.algorithm-summary {
    background: white;
    border: 2px solid #10b981;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.1);
}

.algorithm-summary h4 {
    color: #065f46;
    font-size: 1.2rem;
    margin-bottom: 15px;
    font-weight: bold;
}

.probability-breakdown {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background: #f0fdf4;
    border-radius: 6px;
    border-left: 4px solid #10b981;
}

.summary-label {
    font-weight: 600;
    color: #065f46;
}

.summary-value {
    font-weight: bold;
    color: #047857;
    font-family: 'Courier New', monospace;
    background: #dcfce7;
    padding: 4px 10px;
    border-radius: 4px;
}

.summary-value.final-prob {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    font-size: 1.1rem;
    padding: 6px 12px;
    border-radius: 6px;
    box-shadow: 0 2px 6px rgba(16, 185, 129, 0.3);
}

/* Responsive design for algorithm panel */
@media (max-width: 768px) {
    .algorithm-grid {
        grid-template-columns: 1fr;
    }

    .probability-breakdown {
        grid-template-columns: 1fr;
    }

    .metric {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }

    .metric-value {
        align-self: flex-end;
    }
}

.result-item .address,
.result-item .private-key {
    font-family: monospace;
    font-size: 12px;
    word-break: break-all;
}

.result-item .balance {
    font-weight: bold;
    color: #28a745;
}

.result-item .balance.zero {
    color: #6c757d;
}

/* Check Balance Button */
.check-balance-btn {
    padding: 6px 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.check-balance-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.check-balance-btn:disabled {
    background: #6c757d;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.check-balance-btn.checking {
    background: #ffc107;
    color: #333;
}

.check-balance-btn.success {
    background: #28a745;
    color: white;
}

.check-balance-btn.error {
    background: #dc3545;
    color: white;
}

/* Balance states */
.balance.checking {
    color: #ffc107;
    font-style: italic;
}

.balance.error {
    color: #dc3545;
    font-weight: bold;
}

.stats {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.stats h3 {
    margin-bottom: 20px;
    color: #333;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.stat-item {
    text-align: center;
    padding: 20px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 10px;
}

.stat-value {
    font-size: 2rem;
    font-weight: bold;
    color: #667eea;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 14px;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Probability Statistics */
.probability-stats {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.probability-stats h3 {
    text-align: center;
    margin-bottom: 20px;
    color: #333;
    font-size: 1.5rem;
}

.probability-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.probability-stat-item {
    text-align: center;
    padding: 20px;
    border-radius: 12px;
    transition: transform 0.3s ease;
}

.probability-stat-item:hover {
    transform: translateY(-5px);
}

.probability-stat-item.very-high {
    background: linear-gradient(135deg, #ff6b6b 0%, #feca57 100%);
    color: white;
    box-shadow: 0 5px 15px rgba(255, 107, 107, 0.3);
}

.probability-stat-item.high {
    background: linear-gradient(135deg, #ffeaa7 0%, #fdcb6e 100%);
    color: #333;
    box-shadow: 0 5px 15px rgba(253, 203, 110, 0.3);
}

.probability-stat-item.medium {
    background: linear-gradient(135deg, #a8e6cf 0%, #88d8a3 100%);
    color: white;
    box-shadow: 0 5px 15px rgba(136, 216, 163, 0.3);
}

.probability-stat-item.low {
    background: linear-gradient(135deg, #ddd6fe 0%, #c4b5fd 100%);
    color: #333;
    box-shadow: 0 5px 15px rgba(196, 181, 253, 0.3);
}

.probability-stat-item.very-low {
    background: linear-gradient(135deg, #f1f3f4 0%, #e8eaed 100%);
    color: #666;
    box-shadow: 0 5px 15px rgba(241, 243, 244, 0.3);
}

.probability-stat-value {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.probability-stat-label {
    font-size: 14px;
    font-weight: 600;
}

@media (max-width: 768px) {
    .container {
        padding: 10px;
    }

    .results-header,
    .result-item {
        grid-template-columns: 1fr;
        gap: 5px;
    }

    .results-header span:before {
        content: attr(data-label) ': ';
        font-weight: bold;
    }

    .check-balance-btn {
        width: 100%;
        margin-top: 10px;
    }

    .result-item .address,
    .result-item .private-key {
        font-size: 10px;
    }

    .button-group {
        flex-direction: column;
    }

    .status {
        flex-direction: column;
        gap: 10px;
    }
}
