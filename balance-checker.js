const { parentPort, workerData } = require('worker_threads');
const axios = require('axios');
const { ethers } = require('ethers');

const { address, cryptoType } = workerData;

// API endpoints for balance checking
const API_ENDPOINTS = {
    bitcoin: 'https://blockstream.info/api/address/',
    ethereum: 'https://api.etherscan.io/api',
    bitcoin_alt: 'https://api.blockcypher.com/v1/btc/main/addrs/',
    ethereum_alt: 'https://api.etherscan.io/api',
    litecoin: 'https://api.blockcypher.com/v1/ltc/main/addrs/',
    dogecoin: 'https://api.blockcypher.com/v1/doge/main/addrs/'
};

async function delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

// Enhanced Bitcoin balance checking with multiple APIs
async function checkBitcoinBalance(address) {
    try {
        // Try primary API first
        try {
            const response = await axios.get(`${API_ENDPOINTS.bitcoin}${address}`, {
                timeout: 10000
            });
            
            const balanceSatoshis = response.data.chain_stats.funded_txo_sum - response.data.chain_stats.spent_txo_sum;
            const balance = balanceSatoshis / 100000000; // Convert to BTC
            
            return {
                success: true,
                balance: balance,
                transactions: response.data.chain_stats.tx_count,
                api: 'blockstream.info',
                fullResponse: response.data
            };
        } catch (primaryError) {
            if (primaryError.response && primaryError.response.status === 404) {
                return {
                    success: true,
                    balance: 0,
                    transactions: 0,
                    api: 'blockstream.info',
                    fullResponse: { message: 'Address not found' }
                };
            }
            
            // Try alternative API
            try {
                const altResponse = await axios.get(`${API_ENDPOINTS.bitcoin_alt}${address}/balance`, {
                    timeout: 10000
                });
                
                const balance = altResponse.data.balance / 100000000; // Convert to BTC
                
                return {
                    success: true,
                    balance: balance,
                    transactions: altResponse.data.n_tx || 0,
                    api: 'blockcypher.com',
                    fullResponse: altResponse.data
                };
            } catch (altError) {
                return {
                    success: false,
                    error: `Both APIs failed. Primary: ${primaryError.message}, Alt: ${altError.message}`,
                    balance: 0,
                    transactions: 0
                };
            }
        }
    } catch (error) {
        return {
            success: false,
            error: error.message,
            balance: 0,
            transactions: 0
        };
    }
}

// Enhanced Ethereum balance checking
async function checkEthereumBalance(address) {
    try {
        // Using free Etherscan API
        const response = await axios.get(API_ENDPOINTS.ethereum, {
            params: {
                module: 'account',
                action: 'balance',
                address: address,
                tag: 'latest',
                apikey: 'YourApiKeyToken' // You should get a free API key from Etherscan
            },
            timeout: 10000
        });

        if (response.data.status === '1') {
            const balance = parseFloat(ethers.formatEther(response.data.result));
            
            // Get transaction count
            const txCountResponse = await axios.get(API_ENDPOINTS.ethereum, {
                params: {
                    module: 'proxy',
                    action: 'eth_getTransactionCount',
                    address: address,
                    tag: 'latest',
                    apikey: 'YourApiKeyToken'
                },
                timeout: 10000
            });
            
            const txCount = txCountResponse.data.result ? parseInt(txCountResponse.data.result, 16) : 0;
            
            return {
                success: true,
                balance: balance,
                transactions: txCount,
                api: 'etherscan.io',
                fullResponse: {
                    balance: response.data,
                    transactionCount: txCountResponse.data
                }
            };
        } else {
            return {
                success: false,
                error: response.data.message || 'Unknown error',
                balance: 0,
                transactions: 0,
                fullResponse: response.data
            };
        }
    } catch (error) {
        return {
            success: false,
            error: error.message,
            balance: 0,
            transactions: 0
        };
    }
}

// Check Litecoin balance
async function checkLitecoinBalance(address) {
    try {
        const response = await axios.get(`${API_ENDPOINTS.litecoin}${address}/balance`, {
            timeout: 10000
        });
        
        const balance = response.data.balance / 100000000; // Convert to LTC
        
        return {
            success: true,
            balance: balance,
            transactions: response.data.n_tx || 0,
            api: 'blockcypher.com',
            fullResponse: response.data
        };
    } catch (error) {
        return {
            success: false,
            error: error.message,
            balance: 0,
            transactions: 0
        };
    }
}

// Check Dogecoin balance
async function checkDogecoinBalance(address) {
    try {
        const response = await axios.get(`${API_ENDPOINTS.dogecoin}${address}/balance`, {
            timeout: 10000
        });
        
        const balance = response.data.balance / 100000000; // Convert to DOGE
        
        return {
            success: true,
            balance: balance,
            transactions: response.data.n_tx || 0,
            api: 'blockcypher.com',
            fullResponse: response.data
        };
    } catch (error) {
        return {
            success: false,
            error: error.message,
            balance: 0,
            transactions: 0
        };
    }
}

// Main balance checking function
async function checkBalance() {
    let result;
    
    switch (cryptoType.toLowerCase()) {
        case 'bitcoin':
            result = await checkBitcoinBalance(address);
            break;
        case 'ethereum':
            result = await checkEthereumBalance(address);
            break;
        case 'litecoin':
            result = await checkLitecoinBalance(address);
            break;
        case 'dogecoin':
            result = await checkDogecoinBalance(address);
            break;
        default:
            result = {
                success: false,
                error: `Unsupported cryptocurrency: ${cryptoType}`,
                balance: 0,
                transactions: 0
            };
    }
    
    // Add metadata
    result.address = address;
    result.cryptoType = cryptoType;
    result.timestamp = new Date().toISOString();
    
    parentPort.postMessage(result);
}

// Start the balance check
checkBalance().catch(error => {
    parentPort.postMessage({
        success: false,
        error: error.message,
        address: address,
        cryptoType: cryptoType,
        balance: 0,
        transactions: 0,
        timestamp: new Date().toISOString()
    });
});
