const { parentPort, workerData } = require('worker_threads');
const bitcoin = require('bitcoinjs-lib');
const ecc = require('tiny-secp256k1');
const { ECPairFactory } = require('ecpair');
const { ethers } = require('ethers');
const axios = require('axios');
const crypto = require('crypto');

// Initialize ECPair with tiny-secp256k1
const ECPair = ECPairFactory(ecc);

// Initialize bitcoin library with ECC
bitcoin.initEccLib(ecc);

const { walletCount, cryptoType, intensity, socketId, luckyMode } = workerData;

// API endpoints for balance checking
const API_ENDPOINTS = {
    bitcoin: 'https://blockstream.info/api/address/',
    ethereum: 'https://api.etherscan.io/api',
    bitcoin_alt: 'https://api.blockcypher.com/v1/btc/main/addrs/',
    ethereum_alt: 'https://api.etherscan.io/api',
    litecoin: 'https://api.blockcypher.com/v1/ltc/main/addrs/',
    dogecoin: 'https://api.blockcypher.com/v1/doge/main/addrs/',
    // Bulk checking endpoints
    bitcoin_bulk: 'https://blockstream.info/api/addresses/',
    ethereum_bulk: 'https://api.etherscan.io/api'
};

// Rate limiting - optimized for better throughput
const RATE_LIMIT_DELAY = 100; // Reduced to 100ms for faster checking
let lastRequestTime = 0;

// Bulk checking configuration
const BULK_CHECK_SIZE = 10; // Number of addresses to check in one request

// Common Bitcoin address prefixes that are more likely to have been used
const COMMON_BTC_PREFIXES = [
    '1', '3', 'bc1' // Legacy, SegWit, and Native SegWit
];

// Common Ethereum address patterns (first few characters)
const COMMON_ETH_PATTERNS = [
    '0x1', '0x2', '0x3', '0x4', '0x5', '0x6', '0x7', '0x8', '0x9', '0xa', '0xb', '0xc', '0xd', '0xe', '0xf'
];

// Smart targeting: Focus on address ranges that are more likely to have been used
const SMART_TARGETING = {
    bitcoin: {
        // Focus on addresses that might have been generated in the past
        timeRanges: [
            { start: 1231006505, end: 1231006505 + 31536000 }, // 2009-2010
            { start: 1293840000, end: 1293840000 + 31536000 }, // 2011
            { start: 1325376000, end: 1325376000 + 31536000 }, // 2012
            { start: 1356998400, end: 1356998400 + 31536000 }, // 2013
            { start: 1388534400, end: 1388534400 + 31536000 }, // 2014
            { start: 1420070400, end: 1420070400 + 31536000 }, // 2015
            { start: 1451606400, end: 1451606400 + 31536000 }, // 2016
            { start: 1483228800, end: 1483228800 + 31536000 }, // 2017
            { start: 1514764800, end: 1514764800 + 31536000 }, // 2018
            { start: 1546300800, end: 1546300800 + 31536000 }, // 2019
            { start: 1577836800, end: 1577836800 + 31536000 }, // 2020
            { start: 1609459200, end: 1609459200 + 31536000 }, // 2021
            { start: 1640995200, end: 1640995200 + 31536000 }, // 2022
            { start: 1672531200, end: 1672531200 + 31536000 }, // 2023
        ]
    },
    ethereum: {
        // Focus on early Ethereum addresses
        earlyBlocks: [1, 1000, 10000, 100000, 1000000, 5000000, 10000000]
    }
};

// Advanced prediction algorithms for "I Feel Lucky" feature
const LUCKY_ALGORITHMS = {
    // Common Bitcoin private key patterns that were popular
    bitcoinPatterns: [
        // Early Bitcoin patterns (2009-2012)
        { pattern: [0x01, 0x00, 0x00, 0x00], weight: 0.15 },
        { pattern: [0x00, 0x01, 0x00, 0x00], weight: 0.12 },
        { pattern: [0x00, 0x00, 0x01, 0x00], weight: 0.10 },
        // Common vanity patterns
        { pattern: [0x1A, 0x2B, 0x3C, 0x4D], weight: 0.08 },
        { pattern: [0xDE, 0xAD, 0xBE, 0xEF], weight: 0.06 },
        // Sequential patterns
        { pattern: [0x11, 0x11, 0x11, 0x11], weight: 0.05 },
        { pattern: [0x22, 0x22, 0x22, 0x22], weight: 0.05 },
        // Early mining patterns
        { pattern: [0x00, 0x00, 0x00, 0x01], weight: 0.20 },
        { pattern: [0x00, 0x00, 0x00, 0x02], weight: 0.18 },
        { pattern: [0x00, 0x00, 0x00, 0x03], weight: 0.16 },
    ],
    
    // Ethereum patterns
    ethereumPatterns: [
        // Early Ethereum patterns
        { pattern: [0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01], weight: 0.25 },
        { pattern: [0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02], weight: 0.22 },
        { pattern: [0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03], weight: 0.20 },
        // Common patterns
        { pattern: [0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11], weight: 0.08 },
        { pattern: [0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22], weight: 0.08 },
    ],
    
    // Address entropy patterns (lower entropy = more likely to be generated)
    entropyThresholds: {
        low: 0.3,    // Very predictable patterns
        medium: 0.6,  // Somewhat predictable
        high: 0.9     // Random patterns
    },
    
    // Historical wallet generation patterns
    generationPatterns: {
        // Time-based patterns when wallets were most commonly created
        peakHours: [9, 10, 11, 14, 15, 16, 20, 21, 22], // Peak activity hours
        peakDays: [1, 2, 3, 4, 5], // Weekdays
        peakMonths: [1, 2, 3, 10, 11, 12], // January-March, October-December
    }
};

async function delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

// Generate Bitcoin wallet with smart targeting
async function generateBitcoinWallet() {
    try {
        // Use different generation strategies to increase odds
        const strategy = Math.random();
        let privateKey, keyPair;

        if (strategy < 0.4) {
            // Strategy 1: Use historical time-based entropy (more likely to match past patterns)
            const timeRanges = SMART_TARGETING.bitcoin.timeRanges;
            const randomRange = timeRanges[Math.floor(Math.random() * timeRanges.length)];
            const historicalTime = randomRange.start + Math.floor(Math.random() * (randomRange.end - randomRange.start));
            
            const timeEntropy = Buffer.alloc(32);
            crypto.randomFillSync(timeEntropy);
            timeEntropy.writeUInt32BE(historicalTime, 0);
            privateKey = timeEntropy;
        } else if (strategy < 0.7) {
            // Strategy 2: Use common private key patterns with bias
            privateKey = crypto.randomBytes(32);
            // Set some bits to common patterns that were popular in early Bitcoin days
            privateKey[0] = privateKey[0] | 0x01; // Ensure it's odd
            privateKey[1] = privateKey[1] & 0x7F; // Common pattern
        } else {
            // Strategy 3: Pure random but with bias towards certain ranges
            privateKey = crypto.randomBytes(32);
            // Bias towards addresses that might have been generated in the past
            const bias = Math.floor(Math.random() * 1000000);
            privateKey.writeUInt32BE(bias, 28);
        }

        // Create key pair using ECPair
        keyPair = ECPair.fromPrivateKey(privateKey);

        // Generate hash160 of the public key
        const pubKeyHash = bitcoin.crypto.hash160(keyPair.publicKey);

        // Try different address types to increase odds
        const addressType = Math.random();
        let payment;

        if (addressType < 0.4) {
            // P2PKH (Legacy) - most common
            payment = bitcoin.payments.p2pkh({
                hash: pubKeyHash,
                network: bitcoin.networks.bitcoin
            });
        } else if (addressType < 0.8) {
            // P2SH-P2WPKH (SegWit) - increasingly common
            const witnessScript = bitcoin.payments.p2wpkh({
                hash: pubKeyHash,
                network: bitcoin.networks.bitcoin
            });
            payment = bitcoin.payments.p2sh({
                redeem: witnessScript,
                network: bitcoin.networks.bitcoin
            });
        } else {
            // P2WPKH (Native SegWit) - newer but growing
            payment = bitcoin.payments.p2wpkh({
                hash: pubKeyHash,
                network: bitcoin.networks.bitcoin
            });
        }

        const wallet = {
            address: payment.address,
            privateKey: keyPair.toWIF(),
            type: 'Bitcoin',
            addressType: payment.address.startsWith('1') ? 'Legacy' :
                         payment.address.startsWith('3') ? 'SegWit' : 'Native SegWit'
        };

        // Calculate transaction probability for regular wallets too
        const mockAlgorithm = {
            type: 'standard',
            score: 0.1,
            name: 'Standard Generation'
        };
        wallet.transactionProbability = calculateTransactionProbability(wallet, mockAlgorithm, 'bitcoin');

        return wallet;
    } catch (error) {
        throw new Error(`Bitcoin wallet generation failed: ${error.message}`);
    }
}

// Generate Litecoin wallet (similar to Bitcoin but different network)
async function generateLitecoinWallet() {
    try {
        // Use similar strategies as Bitcoin but with Litecoin network
        const strategy = Math.random();
        let privateKey, keyPair;

        if (strategy < 0.4) {
            // Use historical time-based entropy
            const timeRanges = SMART_TARGETING.bitcoin.timeRanges;
            const randomRange = timeRanges[Math.floor(Math.random() * timeRanges.length)];
            const historicalTime = randomRange.start + Math.floor(Math.random() * (randomRange.end - randomRange.start));
            
            const timeEntropy = Buffer.alloc(32);
            crypto.randomFillSync(timeEntropy);
            timeEntropy.writeUInt32BE(historicalTime, 0);
            privateKey = timeEntropy;
        } else {
            privateKey = crypto.randomBytes(32);
            privateKey[0] = privateKey[0] | 0x01;
        }

        keyPair = ECPair.fromPrivateKey(privateKey);
        const pubKeyHash = bitcoin.crypto.hash160(keyPair.publicKey);

        // Litecoin uses different address prefixes
        const payment = bitcoin.payments.p2pkh({
            hash: pubKeyHash,
            network: {
                ...bitcoin.networks.bitcoin,
                pubKeyHash: 0x30, // Litecoin mainnet
                scriptHash: 0x32  // Litecoin mainnet
            }
        });

        const wallet = {
            address: payment.address,
            privateKey: keyPair.toWIF(),
            type: 'Litecoin',
            addressType: 'Legacy'
        };

        // Calculate transaction probability
        const mockAlgorithm = {
            type: 'standard',
            score: 0.1,
            name: 'Standard Generation'
        };
        wallet.transactionProbability = calculateTransactionProbability(wallet, mockAlgorithm, 'litecoin');

        return wallet;
    } catch (error) {
        throw new Error(`Litecoin wallet generation failed: ${error.message}`);
    }
}

// Generate Dogecoin wallet
async function generateDogecoinWallet() {
    try {
        const strategy = Math.random();
        let privateKey, keyPair;

        if (strategy < 0.4) {
            // Use historical time-based entropy
            const timeRanges = SMART_TARGETING.bitcoin.timeRanges;
            const randomRange = timeRanges[Math.floor(Math.random() * timeRanges.length)];
            const historicalTime = randomRange.start + Math.floor(Math.random() * (randomRange.end - randomRange.start));
            
            const timeEntropy = Buffer.alloc(32);
            crypto.randomFillSync(timeEntropy);
            timeEntropy.writeUInt32BE(historicalTime, 0);
            privateKey = timeEntropy;
        } else {
            privateKey = crypto.randomBytes(32);
            privateKey[0] = privateKey[0] | 0x01;
        }

        keyPair = ECPair.fromPrivateKey(privateKey);
        const pubKeyHash = bitcoin.crypto.hash160(keyPair.publicKey);

        // Dogecoin uses different address prefixes
        const payment = bitcoin.payments.p2pkh({
            hash: pubKeyHash,
            network: {
                ...bitcoin.networks.bitcoin,
                pubKeyHash: 0x1E, // Dogecoin mainnet
                scriptHash: 0x16  // Dogecoin mainnet
            }
        });

        const wallet = {
            address: payment.address,
            privateKey: keyPair.toWIF(),
            type: 'Dogecoin',
            addressType: 'Legacy'
        };

        // Calculate transaction probability
        const mockAlgorithm = {
            type: 'standard',
            score: 0.1,
            name: 'Standard Generation'
        };
        wallet.transactionProbability = calculateTransactionProbability(wallet, mockAlgorithm, 'dogecoin');

        return wallet;
    } catch (error) {
        throw new Error(`Dogecoin wallet generation failed: ${error.message}`);
    }
}

// 🍀 "I Feel Lucky" - Advanced prediction algorithms
async function generateLuckyBitcoinWallet() {
    try {
        const privateKey = Buffer.alloc(32);
        
        // Use advanced prediction algorithms
        const algorithm = selectLuckyAlgorithm('bitcoin');
        
        switch (algorithm.type) {
            case 'pattern':
                // Apply known Bitcoin patterns
                const pattern = algorithm.pattern;
                for (let i = 0; i < Math.min(pattern.length, 4); i++) {
                    privateKey[i] = pattern[i];
                }
                crypto.randomFillSync(privateKey, 4); // Fill rest randomly
                break;
                
            case 'entropy':
                // Generate with specific entropy characteristics
                const entropy = algorithm.entropy;
                if (entropy === 'low') {
                    // Low entropy = more predictable patterns
                    for (let i = 0; i < 8; i++) {
                        privateKey[i] = Math.floor(Math.random() * 10) + 48; // ASCII digits
                    }
                    crypto.randomFillSync(privateKey, 8);
                } else if (entropy === 'medium') {
                    // Medium entropy = some patterns
                    for (let i = 0; i < 4; i++) {
                        privateKey[i] = LUCKY_ALGORITHMS.bitcoinPatterns[Math.floor(Math.random() * 3)].pattern[0];
                    }
                    crypto.randomFillSync(privateKey, 4);
                } else {
                    // High entropy but with bias
                    crypto.randomFillSync(privateKey);
                    // Apply slight bias towards common patterns
                    if (Math.random() < 0.3) {
                        privateKey[0] = privateKey[0] | 0x01;
                    }
                }
                break;
                
            case 'historical':
                // Use historical time patterns
                const timeRanges = SMART_TARGETING.bitcoin.timeRanges;
                const randomRange = timeRanges[Math.floor(Math.random() * timeRanges.length)];
                const historicalTime = randomRange.start + Math.floor(Math.random() * (randomRange.end - randomRange.start));
                
                privateKey.writeUInt32BE(historicalTime, 0);
                crypto.randomFillSync(privateKey, 4);
                break;
                
            default:
                crypto.randomFillSync(privateKey);
        }
        
        // Ensure valid private key
        privateKey[0] = privateKey[0] | 0x01; // Must be odd
        
        const keyPair = ECPair.fromPrivateKey(privateKey);
        const pubKeyHash = bitcoin.crypto.hash160(keyPair.publicKey);
        
        // Use most common address type (Legacy P2PKH)
        const payment = bitcoin.payments.p2pkh({
            hash: pubKeyHash,
            network: bitcoin.networks.bitcoin
        });
        
        const wallet = {
            address: payment.address,
            privateKey: keyPair.toWIF(),
            type: 'Bitcoin',
            addressType: 'Legacy',
            luckyScore: algorithm.score,
            algorithm: algorithm.name
        };
        
        // Calculate transaction probability
        wallet.transactionProbability = calculateTransactionProbability(wallet, algorithm, 'bitcoin');
        
        return wallet;
    } catch (error) {
        throw new Error(`Lucky Bitcoin wallet generation failed: ${error.message}`);
    }
}

async function generateLuckyEthereumWallet() {
    try {
        const privateKey = Buffer.alloc(32);
        
        // Use advanced prediction algorithms
        const algorithm = selectLuckyAlgorithm('ethereum');
        
        switch (algorithm.type) {
            case 'pattern':
                // Apply known Ethereum patterns
                const pattern = algorithm.pattern;
                for (let i = 0; i < Math.min(pattern.length, 8); i++) {
                    privateKey[i] = pattern[i];
                }
                crypto.randomFillSync(privateKey, 8);
                break;
                
            case 'entropy':
                // Generate with specific entropy characteristics
                const entropy = algorithm.entropy;
                if (entropy === 'low') {
                    // Low entropy = more predictable patterns
                    for (let i = 0; i < 16; i++) {
                        privateKey[i] = Math.floor(Math.random() * 16) + 48; // ASCII hex
                    }
                    crypto.randomFillSync(privateKey, 16);
                } else if (entropy === 'medium') {
                    // Medium entropy = some patterns
                    for (let i = 0; i < 8; i++) {
                        privateKey[i] = LUCKY_ALGORITHMS.ethereumPatterns[Math.floor(Math.random() * 3)].pattern[0];
                    }
                    crypto.randomFillSync(privateKey, 8);
                } else {
                    // High entropy but with bias
                    crypto.randomFillSync(privateKey);
                    // Apply slight bias towards common patterns
                    if (Math.random() < 0.25) {
                        privateKey[0] = privateKey[0] | 0x01;
                    }
                }
                break;
                
            case 'historical':
                // Use early Ethereum patterns
                const earlyBlocks = LUCKY_ALGORITHMS.ethereum.earlyBlocks;
                const blockNumber = earlyBlocks[Math.floor(Math.random() * earlyBlocks.length)];
                privateKey.writeUInt32BE(blockNumber, 0);
                crypto.randomFillSync(privateKey, 4);
                break;
                
            default:
                crypto.randomFillSync(privateKey);
        }
        
        const wallet = new ethers.Wallet(privateKey);
        
        const walletData = {
            address: wallet.address,
            privateKey: wallet.privateKey,
            type: 'Ethereum',
            luckyScore: algorithm.score,
            algorithm: algorithm.name
        };
        
        // Calculate transaction probability
        walletData.transactionProbability = calculateTransactionProbability(walletData, algorithm, 'ethereum');
        
        return walletData;
    } catch (error) {
        throw new Error(`Lucky Ethereum wallet generation failed: ${error.message}`);
    }
}

// Select the best lucky algorithm based on current conditions
function selectLuckyAlgorithm(cryptoType) {
    const now = new Date();
    const hour = now.getHours();
    const day = now.getDay();
    const month = now.getMonth() + 1;
    
    // Check if current time matches historical patterns
    const isPeakHour = LUCKY_ALGORITHMS.generationPatterns.peakHours.includes(hour);
    const isPeakDay = LUCKY_ALGORITHMS.generationPatterns.peakDays.includes(day);
    const isPeakMonth = LUCKY_ALGORITHMS.generationPatterns.peakMonths.includes(month);
    
    // Calculate lucky score based on time patterns
    let timeScore = 0;
    if (isPeakHour) timeScore += 0.3;
    if (isPeakDay) timeScore += 0.2;
    if (isPeakMonth) timeScore += 0.2;
    
    // Select algorithm based on crypto type and conditions
    if (cryptoType === 'bitcoin') {
        const patterns = LUCKY_ALGORITHMS.bitcoinPatterns;
        const selectedPattern = patterns[Math.floor(Math.random() * patterns.length)];
        
        return {
            type: 'pattern',
            pattern: selectedPattern.pattern,
            score: selectedPattern.weight + timeScore,
            name: `Bitcoin Pattern (${selectedPattern.weight.toFixed(2)})`
        };
    } else if (cryptoType === 'ethereum') {
        const patterns = LUCKY_ALGORITHMS.ethereumPatterns;
        const selectedPattern = patterns[Math.floor(Math.random() * patterns.length)];
        
        return {
            type: 'pattern',
            pattern: selectedPattern.pattern,
            score: selectedPattern.weight + timeScore,
            name: `Ethereum Pattern (${selectedPattern.weight.toFixed(2)})`
        };
    }
    
    // Fallback to entropy-based
    const entropyTypes = ['low', 'medium', 'high'];
    const selectedEntropy = entropyTypes[Math.floor(Math.random() * entropyTypes.length)];
    
    return {
        type: 'entropy',
        entropy: selectedEntropy,
        score: 0.5 + timeScore,
        name: `Entropy ${selectedEntropy.charAt(0).toUpperCase() + selectedEntropy.slice(1)}`
    };
}

// Advanced mathematical analysis functions for pure prediction
function calculateAddressEntropy(address) {
    const chars = address.split('');
    const charFreq = {};

    // Count character frequencies
    chars.forEach(char => {
        charFreq[char] = (charFreq[char] || 0) + 1;
    });

    // Calculate Shannon entropy
    let entropy = 0;
    const length = chars.length;

    Object.values(charFreq).forEach(freq => {
        const probability = freq / length;
        entropy -= probability * Math.log2(probability);
    });

    return entropy;
}

function analyzeAddressPatterns(address, cryptoType) {
    const patterns = {
        repeatedChars: 0,
        sequentialChars: 0,
        commonSequences: 0,
        checksumStrength: 0,
        mathematicalProperties: 0
    };

    // Analyze repeated characters
    const repeatedMatches = address.match(/(.)\1+/g) || [];
    patterns.repeatedChars = repeatedMatches.length;

    // Analyze sequential patterns
    for (let i = 0; i < address.length - 1; i++) {
        const current = address.charCodeAt(i);
        const next = address.charCodeAt(i + 1);
        if (Math.abs(current - next) === 1) {
            patterns.sequentialChars++;
        }
    }

    // Check for common sequences based on crypto type
    if (cryptoType === 'bitcoin') {
        const commonBtcSequences = ['123', '000', '111', 'abc', 'def'];
        commonBtcSequences.forEach(seq => {
            if (address.toLowerCase().includes(seq)) {
                patterns.commonSequences++;
            }
        });
    } else if (cryptoType === 'ethereum') {
        const commonEthSequences = ['0000', '1111', 'aaaa', 'ffff', '1234'];
        commonEthSequences.forEach(seq => {
            if (address.toLowerCase().includes(seq)) {
                patterns.commonSequences++;
            }
        });
    }

    // Analyze mathematical properties
    let digitSum = 0;
    let hexSum = 0;
    for (let char of address.toLowerCase()) {
        if (char >= '0' && char <= '9') {
            digitSum += parseInt(char);
        } else if (char >= 'a' && char <= 'f') {
            hexSum += char.charCodeAt(0) - 87; // Convert hex to number
        }
    }

    // Check for mathematical patterns
    if (digitSum % 7 === 0 || digitSum % 11 === 0) patterns.mathematicalProperties++;
    if (hexSum % 13 === 0 || hexSum % 17 === 0) patterns.mathematicalProperties++;

    return patterns;
}

function calculateChecksumComplexity(address, cryptoType) {
    let complexity = 0;

    if (cryptoType === 'bitcoin') {
        // Bitcoin address checksum analysis
        const base58Chars = '**********************************************************';
        let validBase58 = true;

        for (let char of address) {
            if (!base58Chars.includes(char)) {
                validBase58 = false;
                break;
            }
        }

        if (validBase58) complexity += 0.3;

        // Check for common Bitcoin address patterns
        if (address.startsWith('1')) complexity += 0.4; // P2PKH
        else if (address.startsWith('3')) complexity += 0.3; // P2SH
        else if (address.startsWith('bc1')) complexity += 0.2; // Bech32

    } else if (cryptoType === 'ethereum') {
        // Ethereum address checksum analysis (EIP-55)
        if (address.startsWith('0x') && address.length === 42) {
            complexity += 0.3;

            // Check mixed case (EIP-55 checksum)
            const hasUpperCase = /[A-F]/.test(address);
            const hasLowerCase = /[a-f]/.test(address);
            if (hasUpperCase && hasLowerCase) {
                complexity += 0.4; // Proper checksum
            }
        }
    }

    return complexity;
}

// Enhanced probability calculation with advanced algorithms
function calculateTransactionProbability(wallet, algorithm, cryptoType) {
    const address = wallet.address;

    // 1. Base probability based on crypto type and historical data
    let baseProbability = 0;
    if (cryptoType === 'bitcoin') {
        baseProbability = 0.0001; // Bitcoin has many addresses
    } else if (cryptoType === 'ethereum') {
        baseProbability = 0.0003; // Ethereum slightly higher activity
    } else {
        baseProbability = 0.00005; // Other cryptos lower
    }

    // 2. Algorithm-based multiplier (enhanced)
    let algorithmMultiplier = 1;
    if (algorithm.type === 'pattern') {
        algorithmMultiplier = 80 + (algorithm.score * 150); // Increased multiplier
    } else if (algorithm.type === 'entropy') {
        if (algorithm.entropy === 'low') {
            algorithmMultiplier = 60; // Low entropy = more predictable
        } else if (algorithm.entropy === 'medium') {
            algorithmMultiplier = 25;
        } else {
            algorithmMultiplier = 8;
        }
    } else if (algorithm.type === 'historical') {
        algorithmMultiplier = 45; // Historical patterns
    }

    // 3. Advanced address analysis
    const entropy = calculateAddressEntropy(address);
    const patterns = analyzeAddressPatterns(address, cryptoType);
    const checksumComplexity = calculateChecksumComplexity(address, cryptoType);

    // 4. Entropy-based scoring (lower entropy = higher probability)
    let entropyMultiplier = 1;
    if (entropy < 3.0) {
        entropyMultiplier = 3.5; // Very low entropy
    } else if (entropy < 3.5) {
        entropyMultiplier = 2.2; // Low entropy
    } else if (entropy < 4.0) {
        entropyMultiplier = 1.5; // Medium entropy
    } else {
        entropyMultiplier = 0.8; // High entropy
    }

    // 5. Pattern-based scoring
    let patternMultiplier = 1;
    patternMultiplier += patterns.repeatedChars * 0.3;
    patternMultiplier += patterns.sequentialChars * 0.2;
    patternMultiplier += patterns.commonSequences * 0.5;
    patternMultiplier += patterns.mathematicalProperties * 0.4;

    // 6. Checksum complexity (simpler checksums = higher probability)
    let checksumMultiplier = 1 + (1 - checksumComplexity) * 2;

    // 7. Address type preferences (some types more commonly used)
    let addressTypeMultiplier = 1;
    if (cryptoType === 'bitcoin') {
        if (address.startsWith('1')) {
            addressTypeMultiplier = 2.5; // Legacy addresses most common
        } else if (address.startsWith('3')) {
            addressTypeMultiplier = 1.8; // SegWit addresses
        } else if (address.startsWith('bc1')) {
            addressTypeMultiplier = 1.2; // Native SegWit
        }
    }

    // 8. Time-based probability adjustments
    const now = new Date();
    const hour = now.getHours();
    const day = now.getDay();
    const month = now.getMonth() + 1;

    let timeMultiplier = 1;
    if (LUCKY_ALGORITHMS.generationPatterns.peakHours.includes(hour)) {
        timeMultiplier *= 1.3;
    }
    if (LUCKY_ALGORITHMS.generationPatterns.peakDays.includes(day)) {
        timeMultiplier *= 1.2;
    }
    if (LUCKY_ALGORITHMS.generationPatterns.peakMonths.includes(month)) {
        timeMultiplier *= 1.25;
    }

    // 9. Calculate final probability with all factors
    const finalProbability = baseProbability *
                            algorithmMultiplier *
                            entropyMultiplier *
                            patternMultiplier *
                            checksumMultiplier *
                            addressTypeMultiplier *
                            timeMultiplier;

    // 10. Apply realistic caps and floors
    const cappedProbability = Math.min(finalProbability * 100, 15); // Max 15%
    const flooredProbability = Math.max(cappedProbability, 0.001); // Min 0.001%

    return flooredProbability;
}

// Generate Ethereum wallet with targeted approach
async function generateEthereumWallet() {
    try {
        let wallet;
        const strategy = Math.random();

        if (strategy < 0.4) {
            // Strategy 1: Use time-based entropy
            const timeEntropy = crypto.randomBytes(32);
            timeEntropy.writeUInt32BE(Math.floor(Date.now() / 1000), 0);
            wallet = new ethers.Wallet(timeEntropy);
        } else if (strategy < 0.7) {
            // Strategy 2: Use common private key patterns
            const privateKey = crypto.randomBytes(32);
            // Set some bits to common patterns
            privateKey[0] = privateKey[0] | 0x01;
            wallet = new ethers.Wallet(privateKey);
        } else {
            // Strategy 3: Pure random with bias
            wallet = ethers.Wallet.createRandom();
        }
        
        const walletData = {
            address: wallet.address,
            privateKey: wallet.privateKey,
            type: 'Ethereum'
        };

        // Calculate transaction probability
        const mockAlgorithm = {
            type: 'standard',
            score: 0.1,
            name: 'Standard Generation'
        };
        walletData.transactionProbability = calculateTransactionProbability(walletData, mockAlgorithm, 'ethereum');

        return walletData;
    } catch (error) {
        throw new Error(`Ethereum wallet generation failed: ${error.message}`);
    }
}

// Enhanced Bitcoin balance checking with multiple APIs
async function checkBitcoinBalance(address) {
    try {
        // Rate limiting
        const now = Date.now();
        if (now - lastRequestTime < RATE_LIMIT_DELAY) {
            await delay(RATE_LIMIT_DELAY - (now - lastRequestTime));
        }
        lastRequestTime = Date.now();

        // Try primary API first
        try {
            const response = await axios.get(`${API_ENDPOINTS.bitcoin}${address}`, {
                timeout: 5000
            });
            
            const balanceSatoshis = response.data.chain_stats.funded_txo_sum - response.data.chain_stats.spent_txo_sum;
            return balanceSatoshis / *********; // Convert to BTC
        } catch (primaryError) {
            if (primaryError.response && primaryError.response.status === 404) {
                return 0;
            }
            
            // Try alternative API
            try {
                const altResponse = await axios.get(`${API_ENDPOINTS.bitcoin_alt}${address}/balance`, {
                    timeout: 5000
                });
                
                return altResponse.data.balance / *********; // Convert to BTC
            } catch (altError) {
                return 0; // Assume 0 if both APIs fail
            }
        }
    } catch (error) {
        return 0; // Return 0 instead of throwing to continue processing
    }
}

// Enhanced Ethereum balance checking
async function checkEthereumBalance(address) {
    try {
        // Rate limiting
        const now = Date.now();
        if (now - lastRequestTime < RATE_LIMIT_DELAY) {
            await delay(RATE_LIMIT_DELAY - (now - lastRequestTime));
        }
        lastRequestTime = Date.now();

        // Using free Etherscan API
        const response = await axios.get(API_ENDPOINTS.ethereum, {
            params: {
                module: 'account',
                action: 'balance',
                address: address,
                tag: 'latest',
                apikey: 'YourApiKeyToken' // You should get a free API key from Etherscan
            },
            timeout: 5000
        });

        if (response.data.status === '1') {
            return parseFloat(ethers.formatEther(response.data.result));
        } else {
            return 0;
        }
    } catch (error) {
        return 0; // Return 0 instead of throwing to continue processing
    }
}

// Check Litecoin balance
async function checkLitecoinBalance(address) {
    try {
        const now = Date.now();
        if (now - lastRequestTime < RATE_LIMIT_DELAY) {
            await delay(RATE_LIMIT_DELAY - (now - lastRequestTime));
        }
        lastRequestTime = Date.now();

        const response = await axios.get(`${API_ENDPOINTS.litecoin}${address}/balance`, {
            timeout: 5000
        });
        
        return response.data.balance / *********; // Convert to LTC
    } catch (error) {
        return 0;
    }
}

// Check Dogecoin balance
async function checkDogecoinBalance(address) {
    try {
        const now = Date.now();
        if (now - lastRequestTime < RATE_LIMIT_DELAY) {
            await delay(RATE_LIMIT_DELAY - (now - lastRequestTime));
        }
        lastRequestTime = Date.now();

        const response = await axios.get(`${API_ENDPOINTS.dogecoin}${address}/balance`, {
            timeout: 5000
        });
        
        return response.data.balance / *********; // Convert to DOGE
    } catch (error) {
        return 0;
    }
}

// Bulk check Bitcoin balances
async function checkBitcoinBalancesBulk(addresses) {
    try {
        const now = Date.now();
        if (now - lastRequestTime < RATE_LIMIT_DELAY) {
            await delay(RATE_LIMIT_DELAY - (now - lastRequestTime));
        }
        lastRequestTime = Date.now();

        // Send console message about the request
        parentPort.postMessage({
            type: 'console',
            message: `🔍 Bitcoin Bulk API Request: Checking ${addresses.length} addresses`,
            level: 'api-request'
        });

        // Blockstream.info supports bulk checking via POST
        const response = await axios.post(`${API_ENDPOINTS.bitcoin_bulk}`, {
            addresses: addresses
        }, {
            timeout: 10000,
            headers: {
                'Content-Type': 'application/json'
            }
        });

        // Send console message about the response
        parentPort.postMessage({
            type: 'console',
            message: `✅ Bitcoin Bulk API Response: ${addresses.length} addresses checked, ${Object.keys(response.data || {}).length} returned data`,
            level: 'api-response'
        });

        const balances = {};
        if (response.data && Array.isArray(response.data)) {
            response.data.forEach(item => {
                if (item.address && item.chain_stats) {
                    const balanceSatoshis = item.chain_stats.funded_txo_sum - item.chain_stats.spent_txo_sum;
                    balances[item.address] = balanceSatoshis / *********; // Convert to BTC
                }
            });
        }

        return balances;
    } catch (error) {
        // Fallback to individual checking if bulk fails
        parentPort.postMessage({
            type: 'console',
            message: `⚠️ Bitcoin Bulk API Failed: ${error.message}. Falling back to individual checks.`,
            level: 'warning'
        });
        return {};
    }
}

// Bulk check Ethereum balances
async function checkEthereumBalancesBulk(addresses) {
    try {
        const now = Date.now();
        if (now - lastRequestTime < RATE_LIMIT_DELAY) {
            await delay(RATE_LIMIT_DELAY - (now - lastRequestTime));
        }
        lastRequestTime = Date.now();

        // Send console message about the request
        parentPort.postMessage({
            type: 'console',
            message: `🔍 Ethereum Bulk API Request: Checking ${addresses.length} addresses`,
            level: 'api-request'
        });

        // Etherscan supports multiple addresses in one request
        const addressString = addresses.join(',');
        const response = await axios.get(API_ENDPOINTS.ethereum_bulk, {
            params: {
                module: 'account',
                action: 'balancemulti',
                address: addressString,
                tag: 'latest',
                apikey: 'YourApiKeyToken'
            },
            timeout: 10000
        });

        // Send console message about the response
        const resultCount = response.data.result ? response.data.result.length : 0;
        parentPort.postMessage({
            type: 'console',
            message: `✅ Ethereum Bulk API Response: ${addresses.length} addresses checked, ${resultCount} returned data`,
            level: 'api-response'
        });

        const balances = {};
        if (response.data.status === '1' && response.data.result) {
            response.data.result.forEach(item => {
                balances[item.account] = parseFloat(ethers.formatEther(item.balance));
            });
        }

        return balances;
    } catch (error) {
        // Fallback to individual checking if bulk fails
        parentPort.postMessage({
            type: 'console',
            message: `⚠️ Ethereum Bulk API Failed: ${error.message}. Falling back to individual checks.`,
            level: 'warning'
        });
        return {};
    }
}

// Bulk check Litecoin balances
async function checkLitecoinBalancesBulk(addresses) {
    try {
        const now = Date.now();
        if (now - lastRequestTime < RATE_LIMIT_DELAY) {
            await delay(RATE_LIMIT_DELAY - (now - lastRequestTime));
        }
        lastRequestTime = Date.now();

        // BlockCypher supports bulk checking
        const response = await axios.post('https://api.blockcypher.com/v1/ltc/main/addrs', {
            addresses: addresses
        }, {
            timeout: 10000,
            headers: {
                'Content-Type': 'application/json'
            }
        });

        const balances = {};
        if (response.data && response.data.addresses) {
            response.data.addresses.forEach(item => {
                if (item.address && item.balance !== undefined) {
                    balances[item.address] = item.balance / *********; // Convert to LTC
                }
            });
        }

        return balances;
    } catch (error) {
        console.log('Bulk Litecoin check failed, falling back to individual checks');
        return {};
    }
}

// Bulk check Dogecoin balances
async function checkDogecoinBalancesBulk(addresses) {
    try {
        const now = Date.now();
        if (now - lastRequestTime < RATE_LIMIT_DELAY) {
            await delay(RATE_LIMIT_DELAY - (now - lastRequestTime));
        }
        lastRequestTime = Date.now();

        // BlockCypher supports bulk checking for Dogecoin
        const response = await axios.post('https://api.blockcypher.com/v1/doge/main/addrs', {
            addresses: addresses
        }, {
            timeout: 10000,
            headers: {
                'Content-Type': 'application/json'
            }
        });

        const balances = {};
        if (response.data && response.data.addresses) {
            response.data.addresses.forEach(item => {
                if (item.address && item.balance !== undefined) {
                    balances[item.address] = item.balance / *********; // Convert to DOGE
                }
            });
        }

        return balances;
    } catch (error) {
        console.log('Bulk Dogecoin check failed, falling back to individual checks');
        return {};
    }
}

// Enhanced wallet generation with better targeting
async function generateAndCheckWallet(type) {
    try {
        let wallet;
        let balance = 0;

        // Generate wallet with enhanced strategies
        if (type === 'bitcoin') {
            wallet = await generateBitcoinWallet();
            balance = await checkBitcoinBalance(wallet.address);
        } else if (type === 'ethereum') {
            wallet = await generateEthereumWallet();
            balance = await checkEthereumBalance(wallet.address);
        } else if (type === 'litecoin') {
            wallet = await generateLitecoinWallet();
            balance = await checkLitecoinBalance(wallet.address);
        } else if (type === 'dogecoin') {
            wallet = await generateDogecoinWallet();
            balance = await checkDogecoinBalance(wallet.address);
        } else {
            throw new Error(`Unsupported crypto type: ${type}`);
        }

        const result = {
            ...wallet,
            balance,
            timestamp: new Date().toISOString(),
            hasBalance: balance > 0
        };

        return result;
    } catch (error) {
        return {
            address: 'ERROR',
            privateKey: 'ERROR',
            type: type || 'Unknown',
            balance: 0,
            error: error.message,
            timestamp: new Date().toISOString(),
            hasBalance: false
        };
    }
}

async function main() {
    let processed = 0;
    const isInfinite = walletCount === -1;
    let foundWithBalance = 0;

    // Enhanced intensity settings for better throughput
    const intensitySettings = {
        normal: { batchSize: 10, concurrentBatches: 3 },
        aggressive: { batchSize: 20, concurrentBatches: 4 },
        extreme: { batchSize: 30, concurrentBatches: 5 }
    };

    const settings = intensitySettings[intensity] || intensitySettings.aggressive;
    const BATCH_SIZE = settings.batchSize;
    const CONCURRENT_BATCHES = settings.concurrentBatches;

    parentPort.postMessage({
        type: 'status',
        message: `Starting ${isInfinite ? 'infinite' : walletCount} ${cryptoType} wallet generation with bulk balance checking...`
    });

    while (isInfinite || processed < walletCount) {
        try {
            // Generate wallets first (no API calls)
            const walletsToCheck = [];
            
            for (let batch = 0; batch < CONCURRENT_BATCHES; batch++) {
                for (let i = 0; i < BATCH_SIZE; i++) {
                    if (!isInfinite && processed + (batch * BATCH_SIZE) + i >= walletCount) {
                        break;
                    }
                    
                    // Generate wallet without checking balance
                    let wallet;
                    if (luckyMode) {
                        // Use "I Feel Lucky" algorithms
                        if (cryptoType === 'bitcoin') {
                            wallet = await generateLuckyBitcoinWallet();
                        } else if (cryptoType === 'ethereum') {
                            wallet = await generateLuckyEthereumWallet();
                        } else {
                            // Fallback to regular generation for other cryptos
                            if (cryptoType === 'bitcoin') {
                                wallet = await generateBitcoinWallet();
                            } else if (cryptoType === 'ethereum') {
                                wallet = await generateEthereumWallet();
                            } else if (cryptoType === 'litecoin') {
                                wallet = await generateLitecoinWallet();
                            } else if (cryptoType === 'dogecoin') {
                                wallet = await generateDogecoinWallet();
                            }
                        }
                    } else {
                        // Regular generation
                        if (cryptoType === 'bitcoin') {
                            wallet = await generateBitcoinWallet();
                        } else if (cryptoType === 'ethereum') {
                            wallet = await generateEthereumWallet();
                        } else if (cryptoType === 'litecoin') {
                            wallet = await generateLitecoinWallet();
                        } else if (cryptoType === 'dogecoin') {
                            wallet = await generateDogecoinWallet();
                        }
                    }
                    
                    walletsToCheck.push(wallet);
                }
            }

            // Process results without automatic balance checking
            for (const wallet of walletsToCheck) {
                processed++;
                const balance = 0; // No automatic balance checking
                
                const result = {
                    ...wallet,
                    balance,
                    timestamp: new Date().toISOString(),
                    hasBalance: balance > 0
                };

                // Log probability for all wallets (enhanced display)
                if (wallet.transactionProbability !== undefined) {
                    const prob = wallet.transactionProbability;
                    let emoji = '🔍';
                    let level = 'info';

                    if (prob >= 5.0) {
                        emoji = '🔥';
                        level = 'success';
                    } else if (prob >= 1.0) {
                        emoji = '⭐';
                        level = 'success';
                    } else if (prob >= 0.5) {
                        emoji = '🎯';
                        level = 'warning';
                    } else if (prob >= 0.1) {
                        emoji = '📊';
                        level = 'info';
                    }

                    parentPort.postMessage({
                        type: 'console',
                        message: `${emoji} ${wallet.address.substring(0, 12)}... → ${prob.toFixed(3)}% transaction probability ${luckyMode ? '(Lucky Mode)' : ''}`,
                        level: level
                    });
                }

                if (result.hasBalance) {
                    foundWithBalance++;
                    // Send immediate notification for wallets with balance
                    parentPort.postMessage({
                        type: 'wallet-with-balance',
                        data: result,
                        processed: processed,
                        total: isInfinite ? 'infinite' : walletCount,
                        foundWithBalance: foundWithBalance
                    });
                }

                parentPort.postMessage({
                    type: 'wallet',
                    data: result,
                    processed: processed,
                    total: isInfinite ? 'infinite' : walletCount,
                    foundWithBalance: foundWithBalance
                });
            }

            // Reduced delay for faster processing
            await delay(50);

        } catch (error) {
            parentPort.postMessage({
                type: 'error',
                message: error.message
            });
        }
    }

    parentPort.postMessage({
        type: 'complete',
        message: `Completed processing ${processed} wallets. Found ${foundWithBalance} with balance.`
    });
}

// Bulk balance checking function
async function checkBalancesBulk(wallets, cryptoType) {
    const addresses = wallets.map(w => w.address);
    const balances = {};

    try {
        // Send console message about bulk checking start
        parentPort.postMessage({
            type: 'console',
            message: `🚀 Starting bulk balance check for ${addresses.length} ${cryptoType} addresses`,
            level: 'bulk-check'
        });

        // Split addresses into chunks for bulk checking
        const chunks = [];
        for (let i = 0; i < addresses.length; i += BULK_CHECK_SIZE) {
            chunks.push(addresses.slice(i, i + BULK_CHECK_SIZE));
        }

        parentPort.postMessage({
            type: 'console',
            message: `📦 Split into ${chunks.length} chunks of ${BULK_CHECK_SIZE} addresses each`,
            level: 'info'
        });

        // Check each chunk
        for (let i = 0; i < chunks.length; i++) {
            const chunk = chunks[i];
            let chunkBalances = {};

            parentPort.postMessage({
                type: 'console',
                message: `🔄 Processing chunk ${i + 1}/${chunks.length} (${chunk.length} addresses)`,
                level: 'info'
            });

            if (cryptoType === 'bitcoin') {
                chunkBalances = await checkBitcoinBalancesBulk(chunk);
            } else if (cryptoType === 'ethereum') {
                chunkBalances = await checkEthereumBalancesBulk(chunk);
            } else if (cryptoType === 'litecoin') {
                chunkBalances = await checkLitecoinBalancesBulk(chunk);
            } else if (cryptoType === 'dogecoin') {
                chunkBalances = await checkDogecoinBalancesBulk(chunk);
            }

            // Merge results
            Object.assign(balances, chunkBalances);

            // Small delay between chunks
            await delay(50);
        }

        const foundWithBalance = Object.values(balances).filter(b => b > 0).length;
        parentPort.postMessage({
            type: 'console',
            message: `✅ Bulk check completed: ${addresses.length} addresses checked, ${foundWithBalance} with balance`,
            level: 'success'
        });

        return balances;
    } catch (error) {
        parentPort.postMessage({
            type: 'console',
            message: `❌ Bulk checking failed: ${error.message}. Falling back to individual checks.`,
            level: 'error'
        });
        
        // Fallback to individual checking
        for (const wallet of wallets) {
            let balance = 0;
            try {
                if (cryptoType === 'bitcoin') {
                    balance = await checkBitcoinBalance(wallet.address);
                } else if (cryptoType === 'ethereum') {
                    balance = await checkEthereumBalance(wallet.address);
                } else if (cryptoType === 'litecoin') {
                    balance = await checkLitecoinBalance(wallet.address);
                } else if (cryptoType === 'dogecoin') {
                    balance = await checkDogecoinBalance(wallet.address);
                }
            } catch (e) {
                balance = 0;
            }
            balances[wallet.address] = balance;
        }

        return balances;
    }
}

// Start the worker
main().catch(error => {
    parentPort.postMessage({
        type: 'error',
        message: error.message
    });
});
