{"name": "tiny-secp256k1", "version": "2.2.4", "description": "A tiny secp256k1 JS", "homepage": "https://github.com/bitcoinjs/tiny-secp256k1#readme", "bugs": {"url": "https://github.com/bitcoinjs/tiny-secp256k1/issues"}, "repository": {"type": "git", "url": "https://github.com/bitcoinjs/tiny-secp256k1.git"}, "license": "MIT", "type": "module", "main": "./lib/cjs/index.cjs", "exports": {".": {"node": {"module": "./lib/index.js", "require": "./lib/cjs/index.cjs", "import": "./lib/index.js"}, "browser": "./lib/index.js", "default": "./lib/index.js"}}, "browser": {"./lib/wasm_loader.js": "./lib/wasm_loader.browser.js", "./lib/rand.js": "./lib/rand.browser.js"}, "types": "./lib/index.d.ts", "files": ["lib"], "dependencies": {"uint8array-tools": "0.0.7"}, "devDependencies": {"@babel/eslint-parser": "^7.22.5", "@babel/plugin-syntax-import-assertions": "^7.22.5", "@types/node": "^14.14.35", "@typescript-eslint/eslint-plugin": "^4.18.0", "@typescript-eslint/parser": "^4.18.0", "browser-run": "^12.0.0", "buffer": "^6.0.3", "eslint": "^7.22.0", "eslint-config-prettier": "^8.1.0", "eslint-plugin-prettier": "^3.3.1", "nyc": "^15.1.0", "path-browserify": "^1.0.1", "prettier": "^2.2.1", "process": "^0.11.10", "sort-package-json": "^1.49.0", "stream-browserify": "^3.0.0", "tap-summary": "^4.0.0", "tape": "^5.2.2", "typescript": "^4.2.3", "webpack": "^5.24.4", "webpack-cli": "^4.5.0"}, "engines": {"node": ">=14.0.0"}}