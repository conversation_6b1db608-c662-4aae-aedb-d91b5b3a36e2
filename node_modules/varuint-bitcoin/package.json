{"name": "varuint-bitcoin", "version": "1.1.2", "description": "encode/decode number as bitcoin variable length integer", "homepage": "https://github.com/bitcoinjs/varuint-bitcoin", "bugs": {"url": "https://github.com/bitcoinjs/varuint-bitcoin/issues"}, "license": "MIT", "author": "<PERSON><PERSON> <<EMAIL>> (http://github.com/fanatid)", "files": ["index.js", "index.d.ts"], "main": "./index.js", "types": "./index.d.ts", "repository": {"type": "git", "url": "https://github.com/bitcoinjs/varuint-bitcoin.git"}, "scripts": {"coverage": "nyc --check-coverage --branches 100 --functions 100 tape test/*.js", "lint": "standard", "test": "npm run lint && npm run unit", "unit": "tape test/*.js"}, "dependencies": {"safe-buffer": "^5.1.1"}, "devDependencies": {"nyc": "^14.1.1", "standard": "*", "tape": "^4.5.1"}}