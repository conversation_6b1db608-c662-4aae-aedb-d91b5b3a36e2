{"name": "bitcoinjs-lib", "version": "6.1.7", "description": "Client-side Bitcoin JavaScript library", "main": "./src/index.js", "types": "./src/index.d.ts", "engines": {"node": ">=8.0.0"}, "keywords": ["bitcoinjs", "bitcoin", "browserify", "javascript", "bitcoinjs"], "scripts": {"build": "npm run clean && tsc -p ./tsconfig.json && npm run formatjs", "build:tests": "npm run clean:jstests && tsc -p ./test/tsconfig.json", "clean": "rimraf src", "clean:jstests": "rimraf 'test/**/!(ts-node-register)*.js'", "coverage-report": "npm run build && npm run nobuild:coverage-report", "coverage-html": "npm run build && npm run nobuild:coverage-html", "coverage": "npm run build && npm run nobuild:coverage", "doc": "typedoc", "format": "npm run prettier -- --write", "formatjs": "npm run prettierjs -- --write", "format:ci": "npm run prettier -- --check && npm run prettierjs -- --check", "gitdiff:ci": "npm run build && git diff --exit-code", "integration": "npm run build && npm run nobuild:integration", "lint": "eslint ts_src/** src/**/*.js", "lint:tests": "eslint test/**/*.spec.ts", "mocha:ts": "mocha --recursive --require test/ts-node-register", "nobuild:coverage-report": "nyc report --reporter=lcov", "nobuild:coverage-html": "nyc report --reporter=html", "nobuild:coverage": "npm run build:tests && nyc --check-coverage --branches 85 --functions 90 --lines 90 mocha && npm run clean:jstests", "nobuild:integration": "npm run mocha:ts -- --timeout 50000 'test/integration/*.ts'", "nobuild:unit": "npm run mocha:ts -- 'test/*.ts'", "prettier": "prettier \"ts_src/**/*.ts\" \"test/**/*.ts\" --ignore-path ./.prettierignore", "prettierjs": "prettier \"src/**/*.js\" --ignore-path ./.prettierignore", "test": "npm run build && npm run format:ci && npm run lint && npm run nobuild:coverage", "unit": "npm run build && npm run nobuild:unit"}, "repository": {"type": "git", "url": "https://github.com/bitcoinjs/bitcoinjs-lib.git"}, "files": ["src"], "dependencies": {"@noble/hashes": "^1.2.0", "bech32": "^2.0.0", "bip174": "^2.1.1", "bs58check": "^3.0.1", "typeforce": "^1.11.3", "varuint-bitcoin": "^1.1.2"}, "devDependencies": {"@types/bs58": "^4.0.0", "@types/bs58check": "^2.1.0", "@types/mocha": "^5.2.7", "@types/node": "^16.11.7", "@types/proxyquire": "^1.3.28", "@types/randombytes": "^2.0.0", "@typescript-eslint/eslint-plugin": "^5.45.0", "@typescript-eslint/parser": "^5.45.0", "bip32": "^4.0.0", "bip39": "^3.1.0", "bip65": "^1.0.1", "bip68": "^1.0.3", "bs58": "^4.0.0", "dhttp": "^3.0.0", "ecpair": "^2.0.1", "eslint": "^8.29.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "hoodwink": "^2.0.0", "minimaldata": "^1.0.2", "mocha": "^10.0.0", "nyc": "^15.1.0", "prettier": "^2.8.0", "proxyquire": "^2.0.1", "randombytes": "^2.1.0", "regtest-client": "0.2.0", "rimraf": "^2.6.3", "tiny-secp256k1": "^2.2.0", "ts-node": "^8.3.0", "typedoc": "^0.25.1", "typescript": "^4.4.4"}, "license": "MIT"}